<?php

/**
 * Application Modules Configuration
 *
 * This file contains all the modules available in the application.
 * Each module defines its permissions, routes, and access control.
 *
 * Module Structure:
 * - id: Unique identifier (snake_case)
 * - title: Display name (Title Case)
 * - description: Brief description
 * - routes: Array of route names
 * - permissions: Array of permission keys
 * - icon: SVG path for the module icon
 * - display: Boolean to show/hide from navigation
 * - authorized_roles: Array of role IDs with access
 */

return [
    /**
     * Dashboard Module
     * Main dashboard with statistics and analytics
     */
    [
        'id' => 'dashboard_module',
        'title' => 'Dashboard',
        'description' => 'module dashboard',
        'routes' => ['dashboard'],
        'permissions' => [
            'access_dashboard_module',

            'show_dashboard',
            'show_dashboard_appointment_stats',
            'show_dashboard_campaign_stats',
            'show_dashboard_report_stats',
            'show_dashboard_performance_stats',
            'show_dashboard_site_stats',
            'show_dashboard_training_stats',
            'show_dashboard_user_stats',
            'show_dashboard_agent_stats',
            'show_dashboard_analytics',

            'export_dashboard_reports'
        ],
        'icon' => '<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m4 12 8-8 8 8M6 10.5V19a1 1 0 0 0 1 1h3v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3h3a1 1 0 0 0 1-1v-8.5"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 4, 5, 6, 7],
    ],
    /**
     * User Management Module
     * Handles user management, roles, and permissions
     */
    [
        'id' => 'user_module',
        'title' => 'Users',
        'description' => 'User module',
        'routes' => [
            'users.index',
            'users.create',
            'users.edit',
            'users.delete',
            'users.show',

            'users.hierarchy.index',
            'users.hierarchy.edit',

            'users.team.members',

            'users.org-chart',

            'users.roles.index',
            'users.roles.create',
            'users.roles.edit',
            'users.roles.delete',
            'users.roles.show',

            'users.permissions.index',
            'users.permissions.create',
            'users.permissions.edit',
            'users.permissions.delete',
            'users.permissions.show'
        ],
        'permissions' => [
            'access_user_module',

            'manage_user',
            'create_user',
            'edit_user',
            'delete_user',
            'show_user',

            'manage_user_hierarchy',
            'edit_user_hierarchy',

            'show_user_org_chart',

            'manage_team_members',

            'manage_user_roles',
            'create_user_roles',
            'edit_user_roles',
            'delete_user_roles',
            'show_user_roles',

            'manage_user_permissions',
            'create_user_permissions',
            'edit_user_permissions',
            'delete_user_permissions',
            'show_user_permissions'
        ],
        'icon' => '<path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2],
    ],
    /**
     * Agent Management Module
     * Manages call center agents and their presence
     */
    [
        'id' => 'agent_module',
        'title' => 'Agents',
        'description' => 'Agent module',
        'routes' => [
            'agents.index',
            'agents.create',
            'agents.edit',
            'agents.delete',
            'agents.show',

            'agents.presence.index',
            'agents.presence.create',
            'agents.presence.edit',
            'agents.presence.delete',
            'agents.presence.show',

            'agents.performance'
        ],
        'permissions' => [
            'access_agent_module',

            'manage_agent',
            'create_agent',
            'edit_agent',
            'delete_agent',
            'show_agent',

            'manage_agent_presence',
            'create_agent_presence',
            'edit_agent_presence',
            'delete_agent_presence',
            'show_agent_presence',

            'show_agent_performance'
        ],
        'icon' => '<path fill-rule="evenodd" d="M12 2a7 7 0 0 0-7 7 3 3 0 0 0-3 3v2a3 3 0 0 0 3 3h1a1 1 0 0 0 1-1V9a5 5 0 1 1 10 0v7.083A2.919 2.919 0 0 1 14.083 19H14a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h1a2 2 0 0 0 1.732-1h.351a4.917 4.917 0 0 0 4.83-4H19a3 3 0 0 0 3-3v-2a3 3 0 0 0-3-3 7 7 0 0 0-7-7Zm1.45 3.275a4 4 0 0 0-4.352.976 1 1 0 0 0 1.452 1.376 2.001 2.001 0 0 1 2.836-.067 1 1 0 1 0 1.386-1.442 4 4 0 0 0-1.321-.843Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 4, 6, 7],
    ],
    /**
     * Campaign Management Module
     * Manages marketing campaigns and their agents
     */
    [
        'id' => 'campaign_module',
        'title' => 'Campaigns',
        'description' => 'Campaign module',
        'routes' => [
            'campaigns.index',
            'campaigns.create',
            'campaigns.edit',
            'campaigns.delete',
            'campaigns.show',

            'campaigns.customers.index',
            'campaigns.customers.create',
            'campaigns.customers.edit',
            'campaigns.customers.delete',
            'campaigns.customers.show',
            'campaigns.customers.contacts.delete',
            'campaigns.customers.documents.delete',


            'campaigns.agents.index',
            'campaigns.agent.remove',
            'campaigns.agent.add',
            'campaigns.agent.observation',
            'campaigns.agent.report',

            'campaigns.reports.index',
            'campaigns.reports.create',
            'campaigns.reports.edit',
            'campaigns.reports.delete',
            'campaigns.reports.show',


            'campaigns.statistics'
        ],
        'permissions' => [
            'access_campaign_module',

            'manage_campaign',
            'create_campaign',
            'edit_campaign',
            'delete_campaign',
            'show_campaign',

            'manage_campaign_customers',
            'create_campaign_customers',
            'edit_campaign_customers',
            'delete_campaign_customers',
            'show_campaign_customers',
            'delete_campaign_customer_contacts',
            'delete_campaign_customer_documents',

            'manage_campaign_agents',
            'add_campaign_agents',
            'remove_campaign_agents',
            'observe_campaign_agents',
            'report_campaign_agents',

            'manage_campaign_reports',
            'create_campaign_reports',
            'show_campaign_reports',
            'edit_campaign_reports',
            'delete_campaign_reports',

            'show_campaign_statistics'
        ],
        'icon' => '<path fill-rule="evenodd" d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 4],
    ],
    /**
     * Training Management Module
     * Manages training sessions and modules
     */
    [
        'id' => 'training_module',
        'title' => 'Training',
        'description' => 'Training module',
        'routes' => [
            'training.index',

            'training.sessions.index',
            'training.sessions.create',
            'training.sessions.show',
            'training.sessions.edit',
            'training.sessions.delete',

            'training.agents.index',
            'training.agents.add',
            'training.agents.remove',
            'training.agents.show',
            'training.agents.validate',
            'training.agents.observe',
            'training.agents.report',

            'training.modules.index',
            'training.modules.create',
            'training.modules.show',
            'training.modules.edit',
            'training.modules.delete',

            'training.statistics.index',
        ],
        'permissions' => [
            'access_training_module',

            'manage_training_sessions',
            'create_training_sessions',
            'show_training_sessions',
            'edit_training_sessions',
            'delete_training_sessions',

            'manage_training_agents',
            'add_training_agents',
            'show_training_agents',
            'remove_training_agents',
            'validate_training_agents',
            'observe_training_agents',
            'report_training_agents',

            'manage_training_modules',
            'create_training_modules',
            'show_training_modules',
            'edit_training_modules',
            'delete_training_modules',

            'show_training_statistics',
        ],
        'icon' => '<path fill-rule="evenodd" d="M12 2c-1.228 0-2.363.379-3.302 1.025a1 1 0 0 0-.247 1.479 5.996 5.996 0 0 1 1.475 3.695 1 1 0 0 0 .586.781l.536.275a1 1 0 0 0 1.288-.313A4.992 4.992 0 0 1 17 8.5a1 1 0 0 0 1.071-.923A4.987 4.987 0 0 0 12 2Zm-8 9.654V16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-4.346a1 1 0 0 0-.553-.894l-6-3a1 1 0 0 0-.894 0l-6 3a1 1 0 0 0-.553.894ZM8 12a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2Zm6 0a1 1 0 1 1 2 0v2a1 1 0 1 1-2 0v-2Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 4, 7, 10],
    ],
    /**
     * Appointment Management Module
     * Manages customer appointments and scheduling
     */
    [
        'id' => 'appointment_module',
        'title' => 'Appointments',
        'description' => 'Appointment module',
        'routes' => [
            'appointments.index',
            'appointments.create',
            'appointments.edit',
            'appointments.delete',
            'appointments.show',
            'appointments.validate',
            'appointments.review',
            'appointments.notify',
            'appointments.notify-bulk',
            'appointments.calendar',
            'appointments.followups',

            'appointments.statistics'
        ],
        'permissions' => [
            'access_appointment_module',

            'manage_appointment',
            'create_appointment',
            'edit_appointment',
            'delete_appointment',
            'show_appointment',

            'validate_appointment',
            'review_appointment',
            'notify_appointment',
            'notify_bulk_appointment',
            'calendar_appointment',
            'followups_appointments',

            'show_appointment_statistics',
        ],
        'icon' => '<path fill-rule="evenodd" d="M5 5a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1 2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a2 2 0 0 1 2-2ZM3 19v-7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Zm6.01-6a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm-10 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 4, 5, 6, 7],
    ],
    /**
     * Report Management Module
     * Manages system reports and analytics
     */
    [
        'id' => 'report_module',
        'title' => 'Reports',
        'description' => 'Report module',
        'routes' => [
            'reports.index',
            'reports.create',
            'reports.edit',
            'reports.delete',
            'reports.show'
        ],
        'permissions' => [
            'access_report_module',
            'manage_report',
            'create_report',
            'edit_report',
            'delete_report',
            'show_report'
        ],
        'icon' => '<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 4h3a1 1 0 0 1 1 1v15a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3m0 3h6m-5-4v4h4V3h-4Z"/>',
        'display' => true,
        'authorized_roles' => [1, 2],// Admin, Director,
    ],

    /**
     * Statistics Module
     * Manages system statistics and metrics
     */
    [
        'id' => 'statistic_module',
        'title' => 'Statistics',
        'description' => 'Statistic module',
        'authorized_roles' => [1, 2],
        'routes' => [
            'statistics.performance',
            'statistics.general',
            'statistics.day',
            'statistics.kpi',
            'statistics.agent',
            'statistics.campaign',
        ],
        'permissions' => [
            'access_statistic_module',
            'show_performance_statistics',
            'show_general_statistics',
            'show_daily_statistics',
            'show_kpi_statistics',
            'show_agent_statistics',
            'show_campaign_statistics',
        ],
        'icon' => '<path d="M13.5 2c-.178 0-.356.013-.492.022l-.074.005a1 1 0 0 0-.934.998V11a1 1 0 0 0 1 1h7.975a1 1 0 0 0 .998-.934l.005-.074A7.04 7.04 0 0 0 22 10.5 8.5 8.5 0 0 0 13.5 2Z"/>
 <path d="M11 6.025a1 1 0 0 0-1.065-.998 8.5 8.5 0 1 0 9.038 9.039A1 1 0 0 0 17.975 13H11V6.025Z"/>',
        'display' => true
    ],

    /**
     * Accountant Module
     * Manages accounting and financial operations
     */
    [
        'id' => 'accountant_module',
        'title' => 'Accountant',
        'description' => 'Accountant module',
        'authorized_roles' => [1, 2, 11], // Admin, Director, Accountant
        'routes' => [
            'accountant.index',
            'accountant.agent.attendance',
            'accountant.payment.management',
            'accountant.payment.history',
            'accountant.salary.calculation',
            'accountant.reports',

            'accountant.invoices',
            'accountant.invoices.create',
            'accountant.invoices.edit',
            'accountant.invoices.show',
            'accountant.invoices.delete'
        ],
        'permissions' => [
            'access_accountant_module',
            'manage_accountant',
            'show_agent_attendance',
            'manage_payments',
            'show_payment_history',
            'manage_salary_calculation',
            'manage_financial_reports',

            'manage_invoice',
            'create_invoice',
            'edit_invoice',
            'show_invoice',
            'delete_invoice'
        ],
        'icon' => '<path fill-rule="evenodd" d="M9 15a6 6 0 1 1 12 0 6 6 0 0 1-12 0Zm3.845-1.855a2.4 2.4 0 0 1 1.2-1.226 1 1 0 0 1 1.992-.026c.426.15.809.408 1.111.749a1 1 0 1 1-1.496 1.327.682.682 0 0 0-.36-.213.997.997 0 0 1-.113-.032.4.4 0 0 0-.394.074.93.93 0 0 0 .455.254 2.914 2.914 0 0 1 1.504.9c.373.433.669 1.092.464 1.823a.996.996 0 0 1-.046.129c-.226.519-.627.94-1.132 1.192a1 1 0 0 1-1.956.093 2.68 2.68 0 0 1-1.227-.798 1 1 0 1 1 1.506-1.315.682.682 0 0 0 .363.216c.038.009.075.02.111.032a.4.4 0 0 0 .395-.074.93.93 0 0 0-.455-.254 2.91 2.91 0 0 1-1.503-.9c-.375-.433-.666-1.089-.466-1.817a.994.994 0 0 1 .047-.134Zm1.884.573.003.008c-.003-.005-.003-.008-.003-.008Zm.55 2.613s-.002-.002-.003-.007a.032.032 0 0 1 .003.007ZM4 14a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0v-4a1 1 0 0 1 1-1Zm3-2a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1Zm6.5-8a1 1 0 0 1 1-1H18a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-.796l-2.341 2.049a1 1 0 0 1-1.24.06l-2.894-2.066L6.614 9.29a1 1 0 1 1-1.228-1.578l4.5-3.5a1 1 0 0 1 1.195-.025l2.856 2.04L15.34 5h-.84a1 1 0 0 1-1-1Z" clip-rule="evenodd"/>',
        'display' => true
    ],

    /**
     * Human Resources Module
     * Manages HR operations and employee data
     */
    [
        'id' => 'human_resource_module',
        'title' => 'Human Resources',
        'description' => 'Human Resource management module',
        'routes' => [
            'hr.index',
            'hr.employees.index',

            'hr.documents.index',
            'hr.documents.create',
            'hr.documents.show',
            'hr.documents.edit',
            'hr.documents.delete',

            'hr.contracts.index',
            'hr.hr-contracts.delete',

            'hr.onboarding.index',
            'hr.onboarding.create',
            'hr.onboarding.show',
            'hr.onboarding.templates',

            'hr.performance.index',
            'hr.attendance.index',

        ],
        'permissions' => [
            'access_hr_module',

            'manage_hr',

            'manage_hr_employees',

            'manage_hr_documents',
            'create_hr_documents',
            'show_hr_documents',
            'edit_hr_documents',
            'delete_hr_documents',

            'manage_hr_contracts',
            'delete_hr_contracts',

            'manage_hr_onboarding',
            'create_hr_onboarding',
            'show_hr_onboarding',
            'manage_hr_onboarding_templates',

            'show_hr_performance',
            'manage_hr_attendance',
        ],
        'icon' => '<path fill-rule="evenodd" d="M8 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8Zm.5 4h-1a.5.5 0 0 0-.5.5V12h2v-.5a.5.5 0 0 0 0-1ZM7 14v6h2v-6H7Zm4 0a1 1 0 0 1 1-1h.5a.5.5 0 0 0 .5-.5V12h-2v2Zm3.5-3h.5a.5.5 0 0 1 .5.5V12h-1v-.5a.5.5 0 0 0 0-1ZM14 14v6h2v-6h-2Zm-9 0a1 1 0 0 1 1-1h.5a.5.5 0 0 0 0-1H6v2Zm-1.5-3H3a.5.5 0 0 0-.5.5V12h1v-.5a.5.5 0 0 1 .5-.5ZM2 14v6h2v-6H2Zm16 0a1 1 0 0 0-1-1h-1v2h2v-1Zm-1-3a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm-7-8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z" clip-rule="evenodd"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 12], // Admin, Director, HR Manager
    ],

    /**
     * Site Management Module
     * Manages physical sites and platforms
     */
    [
        'id' => 'site_management_module',
        'title' => 'Sites',
        'description' => 'Manage sites and platforms',
        'routes' => [
            'sites.index',
            'sites.create',
            'sites.edit',
            'sites.show',
            'sites.delete',

            'sites.platforms.index',
            'sites.platforms.create',
            'sites.platforms.edit',
            'sites.platforms.show',
            'sites.platforms.delete',

            'sites.equipments.index',
            'sites.equipments.create',
            'sites.equipments.edit',
            'sites.equipments.show',
            'sites.equipments.delete',

            'sites.reports.index',
            'sites.reports.create',
            'sites.reports.edit',
            'sites.reports.show',
            'sites.reports.delete',

            'sites.personnels.index',
            'sites.personnels.assign',

            'sites.statistics.index'
        ],
        'permissions' => [
            'access_site_management_module',

            'manage_site',
            'create_site',
            'edit_site',
            'show_site',
            'delete_site',

            'manage_platform',
            'create_platform',
            'edit_platform',
            'show_platform',
            'delete_platform',

            'manage_equipment',
            'create_equipment',
            'edit_equipment',
            'show_equipment',
            'delete_equipment',

            'manage_site_reports',
            'create_site_reports',
            'edit_site_reports',
            'show_site_reports',
            'delete_site_reports',

            'manage_site_personnel',
            'assign_site_personnel',

            'manage_site_statistics'
        ],
        'icon' => '<path d="M10 6a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm-6 4a6 6 0 1 1 12 0 6 6 0 0 1-12 0zm14-1h-1.5a.5.5 0 0 0 0 1H18a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-1.5a.5.5 0 0 0-1 0V15a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2z"/>',
        'display' => true,
        'authorized_roles' => [1, 2, 3, 11, 12], // Admin, Director, Manager, Accountant, HR Manager
    ],
    [
        'id' => 'call_center_module',
        'title'=> 'Call Centers',
        'description' => 'Manage call centers',
        'routes' => [
            'call-centers.index',
            'call-centers.create',
            'call-centers.edit',
            'call-centers.show',
            'call-centers.delete',

            'call-centers.sites.index',
            'call-centers.sites.create',
            'call-centers.sites.edit',
            'call-centers.sites.show',
            'call-centers.sites.delete',

            'call-centers.departments.index',
            'call-centers.departments.create',
            'call-centers.departments.edit',
            'call-centers.departments.show',
            'call-centers.departments.delete',
        ],
        'permissions' => [
            'access_call_center_module',
            'manage_call_center',
            'create_call_center',
            'edit_call_center',
            'show_call_center',
            'delete_call_center',

            'manage_call_center_sites',
            'create_call_center_site',
            'edit_call_center_site',
            'show_call_center_site',
            'delete_call_center_site',

            'manage_call_center_departments',
            'create_call_center_department',
            'edit_call_center_department',
            'show_call_center_department',
            'delete_call_center_department',
        ],
        'icon' => '<path d="M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5zm12 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0-4a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm-4 4a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0-4a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm-4 4a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm0-4a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/>',
        'display'     => true,
        'authorized_roles' => [1, 2], // Admin, Director
    ],

    [
        'id' => 'document_management_module',
        'title'=> 'Documents',
        'description' => 'Document management module',
        'routes' => [
            'documents.dashboard',
            'documents.verification',
            'documents.expiration',
            'documents.view',
        ],
        'permissions' => [
            'access_document_management_module',
            'show_document_dashboard',
            'verify_document',
            'check_document_expiration',
            'show_document',
        ],
        'icon' => '<path fill-rule="evenodd" d="M4 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4zm2 0v12h8V4H6zm10 0v12a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-2a2 2 0 0 1-2-2zM8 6a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1zm0 4a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1zm0 4a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1z" clip-rule="evenodd"/>',
        'display'     => true,
        'authorized_roles' => [1, 2, 7, 12], // Admin, Director, Agent, HR Manager
    ],

    [
        'id' => 'skills_certifications_module',
        'title'=> 'Skills & Certifications',
        'description' => 'Skills and certifications management for call center agents',
        'routes' => [
            'skills.index',
            'skills.create',
            'skills.edit',
            'skills.show',

            'certifications.index',
            'certifications.create',
            'certifications.edit',
            'certifications.show',
        ],
        'permissions' => [
            'access_skills_certifications_module',

            'manage_skills',
            'create_skill',
            'edit_skill',
            'show_skill',

            'manage_certifications',
            'create_certification',
            'edit_certification',
            'show_certification',
        ],
        'icon' => '<path d="M9.68 2.75a.75.75 0 0 1 .75.75v.5h3.17a.75.75 0 0 1 0 1.5h-3.17v.5a.75.75 0 0 1-1.5 0v-.5H5.75a.75.75 0 0 1 0-1.5h3.18v-.5a.75.75 0 0 1 .75-.75zm0 5a.75.75 0 0 1 .75.75v.5h8.42a.75.75 0 0 1 0 1.5H10.43v.5a.75.75 0 0 1-1.5 0v-.5H5.75a.75.75 0 0 1 0-1.5h3.18v-.5a.75.75 0 0 1 .75-.75zm0 5a.75.75 0 0 1 .75.75v.5h3.17a.75.75 0 0 1 0 1.5h-3.17v.5a.75.75 0 0 1-1.5 0v-.5H5.75a.75.75 0 0 1 0-1.5h3.18v-.5a.75.75 0 0 1 .75-.75zm8.42 5.25a.75.75 0 0 0 0-1.5H5.75a.75.75 0 0 0 0 1.5h12.35z"/>',
        'display'     => true,
        'authorized_roles' => [1, 2, 3, 4], // Admin, Director, Manager, Supervisor
    ],

    [
        'id' => 'call_quality_module',
        'title'=> 'Call Quality',
        'description' => 'Call quality monitoring and evaluation',
        'routes' => [
            'call-quality.dashboard',
            'call-quality.calls',
            'call-quality.call-show',

            'call-quality.evaluations',
            'call-quality.evaluation-edit',
            'call-quality.bulk-evaluate',

            'call-quality.criteria',
            'call-quality.reports',
            'call-quality.settings',
        ],
        'permissions' => [
            'access_call_quality_module',

            'manage_call_quality',
            'manage_call_quality_calls',
            'show_call_quality_calls',
            'manage_call_quality_evaluations',
            'edit_call_quality_evaluations',
            'manage_call_quality_criteria',
            'show_call_quality_reports',
            'manage_call_quality_settings',
        ],
        'icon' => '<path d="M2 5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-2.586l-1.707 1.707A1 1 0 0 1 15 19h-6a1 1 0 0 1-.707-.293L6.586 17H4a2 2 0 0 1-2-2V5zm18 0H4v10h3a1 1 0 0 1 .707.293L9.414 17h5.172l1.707-1.707A1 1 0 0 1 17 15h3V5zM8 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm4 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2zm5-1a1 1 0 1 0-2 0 1 1 0 0 0 2 0z"/>',
        'display'     => true,
        'authorized_roles' => [1, 2, 3, 4], // Admin, Director, Manager, Supervisor
    ],

    [
        'id' => 'setting_module',
        'title'=> 'Settings',
        'description' => 'Setting module',
        'routes' => [
            'settings.general.index',
            'settings.calls',
            'settings.campaigns',
            'settings.notifications',

            'settings.integrations',

            'settings.permissions.roles',
            'settings.permissions.users',

            'settings.departments',
            'settings.departments.create',
            'settings.departments.edit',
            'settings.departments.delete',
        ],
        'permissions' => [
            'access_setting_module',
            'manage_general_settings',
            'manage_call_settings',
            'manage_campaign_settings',
            'manage_notification_settings',

            'manage_integrations',

            'manage_roles',
            'manage_user_permissions',

            'manage_departments',
            'create_departments',
            'edit_departments',
            'delete_departments',
        ],
        'icon' => '<path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"/>',
        'display'     => true,
        'authorized_roles' => [1, 2, 3, 4], // Admin, Director, Manager, Supervisor
    ],
];
