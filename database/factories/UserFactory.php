<?php

namespace Database\Factories;

use App\Models\Campaign;
use App\Models\Media;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;


    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'birth_date' => $this->faker->dateTimeBetween('-50 years', '-18 years')->format('Y-m-d'),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone_number' => $this->faker->optional()->phoneNumber(),
            'address' => $this->faker->optional()->streetAddress(),
            'city' => $this->faker->optional()->city(),
            'country' => $this->faker->optional()->country(),
            'campaign_id' => Campaign::inRandomOrder()->first()?->id,
            'registration_number' => $this->faker->unique()->numerify('EMP-#####'),
            'hire_date' => $this->faker->dateTimeBetween('-5 years', 'now')->format('Y-m-d'),
            'status' => $this->faker->randomElement(['in_training', 'actif', 'inactif']),
            'role_id' => Role::inRandomOrder()->first()?->id,
            'remember_token' => Str::random(10),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Configure the factory to add a profile picture after creating a user.
     */
    public function configure()
    {
        return $this->afterCreating(function (User $user) {
            // 50% de chance d’ajouter une photo de profil
            if ($this->faker->boolean(50)) {
                Media::create([
                    'mediable_id' => $user->id,
                    'mediable_type' => User::class,
                    'file_name' => $this->faker->word() . '.jpg',
                    'file_path' => 'media/profile_pictures/' . $user->id . '/' . $this->faker->uuid() . '.jpg',
                    'mime_type' => 'image/jpeg',
                    'category' => 'profile_picture',
                    'uploaded_by' => $user->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        });
    }

    /**
     * Indicate that the user's email is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
