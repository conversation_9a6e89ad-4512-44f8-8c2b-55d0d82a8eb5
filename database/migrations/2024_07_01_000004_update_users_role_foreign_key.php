<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the foreign key constraint (SQLite doesn't support dropping constraints directly)
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
        });

        // Add back the foreign key constraint
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('role_id')
                ->references('id')
                ->on('roles')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the new foreign key constraint
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_name']);
        });

        // Add back the original foreign key constraint
        Schema::table('users', function (Blueprint $table) {
            $table->string('role_name')
                ->references('name')
                ->on('roles')
                ->onDelete('cascade');
        });
    }
};
