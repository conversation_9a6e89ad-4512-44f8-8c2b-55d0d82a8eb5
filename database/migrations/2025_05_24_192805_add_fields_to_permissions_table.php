<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->string('module')->nullable()->after('guard_name');
            $table->text('description')->nullable()->after('module');
            $table->boolean('is_system')->default(false)->after('description');
            $table->string('group')->nullable()->after('is_system');
            
            // Add index for faster lookups
            $table->index('module');
            $table->index('group');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropIndex(['module']);
            $table->dropIndex(['group']);
            
            $table->dropColumn([
                'module',
                'description',
                'is_system',
                'group'
            ]);
        });
    }
};
