<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Media;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role as SpatieRole;
use App\Models\Permission as SpatiePermission;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // First, create the admin user
        $adminRole = Role::where('name', Role::ADMIN)->first();
        
        if (!$adminRole) {
            throw new \Exception('Admin role not found');
        }

        $admin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'first_name' => 'Admin',
            'last_name' => 'User',
            'status' => 'actif',
            'registration_number' => 'EMP-ADMIN',
            'hire_date' => now()->subYear(),
            'role_id' => $adminRole->id,
            'guard_name' => 'web',
            'password' => bcrypt('password'),
        ]);

        // Get all permissions from the modules configuration
        $modules = config('modules');
        
        // Create an array of all permissions
        $allPermissions = [];
        foreach ($modules as $module) {
            if (isset($module['permissions'])) {
                foreach ($module['permissions'] as $permission) {
                    $allPermissions[] = $permission;
                }
            }
        }

        // Deduplicate
        $allPermissions = array_unique($allPermissions);

        // Create permissions if they don't exist
        foreach ($allPermissions as $permission) {
            if (!SpatiePermission::where('name', $permission)->exists()) {
                SpatiePermission::create(['name' => $permission, 'guard_name' => 'web']);
            }
        }

        // Get or create the administrator role
        $adminRole = SpatieRole::firstOrCreate(
            ['name' => Role::ADMIN, 'guard_name' => 'web']
        );

        // Assign all permissions to the administrator role
        $adminRole->syncPermissions($allPermissions);

        // Assign the admin user to the administrator role
        if (! $admin->hasRole($adminRole->name)) {
            $admin->assignRole($adminRole);
        }

        Media::create([
            'mediable_id' => $admin->id,
            'mediable_type' => User::class,
            'file_name' => 'admin_profile.jpg',
            'file_path' => 'media/profile_pictures/admin_profile.jpg',
            'mime_type' => 'image/jpeg',
            'category' => 'profile_picture',
            'uploaded_by' => $admin->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Get all roles again after admin creation
        $roles = SpatieRole::all();
        
        if ($roles->isEmpty()) {
            throw new \Exception('No roles found after admin creation');
        }

        // Store admin ID in config for other seeders
        config(['miamboo.admin_user_id' => $admin->id]);
        
        // Debug admin user creation
        \Illuminate\Support\Facades\Log::info('Admin user created with ID: ' . $admin->id);

        // Crée 3 utilisateurs par rôle
        foreach ($roles as $role) {
            User::factory(3)->create([
                'role_id' => $role->id,
                'guard_name' => 'web',
            ])->each(function ($user) use ($role) {
                $user->assignRole($role);
            });
        }

        // Crée 10 utilisateurs additionnels avec rôles assignés aléatoirement
        User::factory(10)->create()->each(function ($user) use ($roles) {
            $role = $roles->random();
            $user->role_id = $role->id;
            $user->guard_name = 'web';
            $user->save();
            $user->assignRole($role);
        });

        // Assure que tous les users ont un role_id
        User::whereNull('role_id')->each(function (User $user) use ($roles) {
            $user->role_id = $roles->random()->id;
            $user->save();
        });

        if (User::whereNull('role_id')->count() > 0) {
            throw new \Exception('Some users still have null role_id');
        }

        if (User::count() < 1) {
            throw new \Exception('No users were created');
        }
    }
}
