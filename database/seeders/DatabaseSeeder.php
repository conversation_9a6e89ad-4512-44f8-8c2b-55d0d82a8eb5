<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run the role seeder first
        $this->call(RoleSeeder::class);
        
        // Run permission seeder
        $this->call(PermissionSeeder::class);
        
        // Run role-permission seeder
        $this->call(RolePermissionSeeder::class);
        
        // Run customer seeder
        $this->call(CustomerSeeder::class);
        
        // Run user seeder
        $this->call(UserSeeder::class);
        
        // Run call center seeder
        $this->call(CallCenterSeeder::class);
        
        // Run site seeder
        $this->call(SiteSeeder::class);
        
        // Run platform seeder
        $this->call(PlatformSeeder::class);
        
        // Run department seeder
        $this->call(DepartmentSeeder::class);
        
        // Run team seeder
        $this->call(TeamSeeder::class);
        
        // Run onboarding template seeder
        $this->call(OnboardingTemplateSeeder::class);
        
        // Run campaign seeder
        $this->call(CampaignSeeder::class);
        
        // Run training seeder
        $this->call(TrainingSeeder::class);
        
        // Run shift seeder
        $this->call(ShiftSeeder::class);
        
        // Run appointment seeder
        $this->call(AppointmentSeeder::class);
        
        // Run report seeder
        $this->call(ReportSeeder::class);
        
        // Run payment seeder
        $this->call(PaymentSeeder::class);
        
        // Run settings seeder
        $this->call(SettingsSeeder::class);
    }
}
