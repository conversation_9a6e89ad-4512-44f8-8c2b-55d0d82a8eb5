<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Role;
use App\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing role-permission assignments
        DB::table('role_permissions')->truncate();

        // Get all permissions from modules configuration
        $modules = config('modules');
        
        // Define role-permission mappings based on modules configuration
        $rolePermissions = [
            // Admin has all permissions
            Role::ADMIN => Permission::all()->pluck('id')->toArray(),

            // Director permissions - all permissions from modules
            Role::DIRECTOR => [],

            // Manager permissions - most permissions from modules
            Role::MANAGER => [],

            // Supervisor permissions - limited permissions
            Role::SUPERVISOR => [],

            // Campaign Supervisor permissions - limited permissions
            Role::CAMPAIGN_SUPERVISOR => [],

            // Quality Controller permissions - specific permissions
            Role::QUALITY_CONTROLLER => [],

            // Agent permissions - basic permissions
            Role::AGENT => [],

            // HR Manager permissions - HR-related permissions
            Role::HR_MANAGER => [],
        ];

        // Populate role permissions based on modules configuration
        foreach ($modules as $module) {
            if (!isset($module['permissions'])) {
                continue;
            }

            foreach ($module['permissions'] as $permission) {
                $permissionName = $permission;

                // Get permission ID
                $permissionObj = Permission::where('name', $permissionName)->first();

                if (!$permissionObj) {
                    continue;
                }

                // Add permission for each role based on authorized_roles
                foreach ($module['authorized_roles'] as $roleId) {
                    if (isset($rolePermissions[$roleId])) {
                        if (!in_array($permissionObj->id, $rolePermissions[$roleId])) {
                            $rolePermissions[$roleId][] = $permissionObj->id;
                        }
                    }
                }
            }
        }

        // Insert role-permission assignments
        foreach ($rolePermissions as $roleId => $permissionNames) {
            if (is_array($permissionNames)) {
                $permissionIds = Permission::whereIn('name', $permissionNames)->pluck('id')->toArray();
            } else {
                $permissionIds = $permissionNames; // Already an array of IDs (for admin)
            }

            // Get the role by name
            $role = Role::where('name', $roleId)->first();
            if (!$role) {
                continue;
            }

            foreach ($permissionIds as $permissionId) {
                DB::table('role_permissions')->insert([
                    'role_id' => $role->id,
                    'permission_id' => $permissionId,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }
}
