<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\CallQuality\CallIndex;
use App\Livewire\CallQuality\CallQualityPage;
use App\Livewire\CallQuality\CallShow;
use App\Livewire\CallQuality\CriteriaIndex;
use App\Livewire\CallQuality\Dashboard;
use App\Livewire\CallQuality\EvaluationEdit;
use App\Livewire\CallQuality\EvaluationIndex;
use App\Livewire\CallQuality\QualityReports;
use App\Livewire\CallQuality\QualitySettings;

Route::middleware(['auth', 'verified'])->group(function () {
    // Call Quality Dashboard
    Route::get('/call-quality', function () {
        return redirect()->route('call-quality.dashboard');
    })->name('call-quality');

    // Call Quality Dashboard
    Route::get('/call-quality/dashboard', CallQualityPage::class)
        ->name('call-quality.dashboard');

    // Calls
    Route::get('/call-quality/calls', CallQualityPage::class)
        ->defaults('component', 'call-quality.call-index')
        ->name('call-quality.calls');
    
    Route::get('/call-quality/calls/{call}', CallShow::class)
        ->name('call-quality.call-show');

    // Evaluations
    Route::get('/call-quality/evaluations', CallQualityPage::class)
        ->defaults('component', 'call-quality.evaluation-index')
        ->name('call-quality.evaluations');
    
    Route::get('/call-quality/evaluations/{evaluation}/edit', EvaluationEdit::class)
        ->name('call-quality.evaluation-edit');
    
    Route::get('/call-quality/bulk-evaluate', function () {
        return redirect()->route('call-quality.calls');
    })->name('call-quality.bulk-evaluate');

    // Criteria
    Route::get('/call-quality/criteria', CallQualityPage::class)
        ->defaults('component', 'call-quality.criteria-index')
        ->name('call-quality.criteria');

    // Reports
    Route::get('/call-quality/reports', CallQualityPage::class)
        ->defaults('component', 'call-quality.quality-reports')
        ->name('call-quality.reports');

    // Settings
    Route::get('/call-quality/settings', CallQualityPage::class)
        ->defaults('component', 'call-quality.quality-settings')
        ->name('call-quality.settings');
});
