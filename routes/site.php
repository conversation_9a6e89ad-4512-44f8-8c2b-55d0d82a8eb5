<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Sites\SitePage;

// Site routes
Route::middleware(['auth'])->prefix('sites')->name('sites.')->group(function () {
    // Site Management
    Route::get('/',SitePage::class)
        ->defaults('component', 'sites.site-index')
        ->name('index')
        ->middleware('permission:manage_sites');
    Route::get('/create',SitePage::class)
        ->defaults('component', 'sites.site-create')
        ->name('create')
        ->middleware('permission:create_site');
    Route::get('/{site}/edit',SitePage::class)
        ->defaults('component', 'sites.site-edit')
        ->name('edit')
        ->middleware('permission:edit_site');
    Route::get('/{site}/delete',SitePage::class)
        ->defaults('component', 'sites.site-delete')
        ->name('delete')
        ->middleware('permission:delete_site');
    Route::get('/{site}',SitePage::class)
        ->defaults('component', 'sites.site-show')
        ->name('show')
        ->middleware('permission:show_site');

    // Platform Submodule
    Route::get('/platforms',SitePage::class)
        ->defaults('component', 'sites.site-platforms')
        ->name('platforms.index')
        ->middleware('permission:manage_platforms');
    Route::get('/platforms/create',SitePage::class)
        ->defaults('component', 'sites.site-platform-create')
        ->name('platforms.create')
        ->middleware('permission:create_platform');
    Route::get('/platforms/{platform}/edit',SitePage::class)
        ->defaults('component', 'sites.site-platform-edit')
        ->name('platforms.edit')
        ->middleware('permission:edit_platform');
    Route::get('/platforms/{platform}/delete',SitePage::class)
        ->defaults('component', 'sites.site-platform-delete')
        ->name('platforms.delete')
        ->middleware('permission:delete_platform');
    Route::get('/platforms/{platform}',SitePage::class)
        ->defaults('component', 'sites.site-platform-show')
        ->name('platforms.show')
        ->middleware('permission:show_platform');

    // Equipment Submodule
    Route::get('/equipments',SitePage::class)
        ->defaults('component', 'sites.site-equipments')
        ->name('equipments.index')
        ->middleware('permission:manage_equipment');
    Route::get('/equipments/create',SitePage::class)
        ->defaults('component', 'sites.site-equipment-create')
        ->name('equipments.create')
        ->middleware('permission:create_equipment');
    Route::get('/equipments/{equipment}/edit',SitePage::class)
        ->defaults('component', 'sites.site-equipment-edit')
        ->name('equipments.edit')
        ->middleware('permission:edit_equipment');
    Route::get('/equipments/{equipment}/delete',SitePage::class)
        ->defaults('component', 'sites.site-equipment-delete')
        ->name('equipments.delete')
        ->middleware('permission:delete_equipment');
    Route::get('/equipments/{equipment}',SitePage::class)
        ->defaults('component', 'sites.site-equipment-show')
        ->name('equipments.show')
        ->middleware('permission:show_equipment');

    // Reports Submodule
    Route::get('/reports',SitePage::class)
        ->defaults('component', 'sites.site-reports')
        ->name('reports.index')
        ->middleware('permission:manage_site_reports');
    Route::get('/reports/create',SitePage::class)
        ->defaults('component', 'sites.site-report-create')
        ->name('reports.create')
        ->middleware('permission:create_site_reports');
    Route::get('/reports/{report}/edit',SitePage::class)
        ->defaults('component', 'sites.site-report-edit')
        ->name('reports.edit')
        ->middleware('permission:edit_site_reports');
    Route::get('/reports/{report}/delete',SitePage::class)
        ->defaults('component', 'sites.site-report-delete')
        ->name('reports.delete')
        ->middleware('permission:delete_site_reports');
    Route::get('/reports/{report}',SitePage::class)
        ->defaults('component', 'sites.site-report-show')
        ->name('reports.show')
        ->middleware('permission:show_site_reports');
    

    // Personnel Submodule
    Route::get('/personnels',SitePage::class)
        ->defaults('component', 'sites.site-personnels')
        ->name('personnels.index')
        ->middleware('permission:manage_site_personnel');
    Route::get('/personnels/assign',SitePage::class)
        ->defaults('component', 'sites.site-personnels-assign')
        ->name('personnels.assign')
        ->middleware('permission:assign_site_personnel');

    // Statistics Submodule
    Route::get('/statistics',SitePage::class)
        ->defaults('component', 'sites.site-statistics')
        ->name('statistics.index')
        ->middleware('permission:manage_site_statistics');
});