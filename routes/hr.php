<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Hr\HrPage;

// Human Resources routes
Route::middleware(['auth'])->prefix('hr')->name('hr.')->group(function () {
    Route::get('/',HrPage::class)
        ->defaults('component', 'hr.hr-index')
        ->name('index');

    // Employee Management
    Route::get('/employees',HrPage::class)
        ->defaults('component', 'hr.hr-employees')
        ->name('employees')
        ->middleware('permission:manage_employees');

    // Document Management
    Route::get('/documents',HrPage::class)
        ->defaults('component', 'hr.hr-documents')
        ->name('documents')
        ->middleware('permission:manage_hr_documents');
    Route::get('/documents/create',HrPage::class)
        ->defaults('component', 'hr.hr-documents-create')
        ->name('documents.create')  
        ->middleware('permission:create_hr_documents');
    Route::get('/documents/{document}',HrPage::class)
        ->defaults('component', 'hr.hr-documents-show')
        ->name('documents.show')
        ->middleware('permission:show_hr_documents');
    Route::get('/documents/{document}/edit',HrPage::class)
        ->defaults('component', 'hr.hr-documents-edit')
        ->name('documents.edit')
        ->middleware('permission:edit_hr_documents');
    Route::get('/documents/{document}/delete',HrPage::class)
        ->defaults('component', 'hr.hr-documents-delete')
        ->name('documents.delete')
        ->middleware('permission:delete_hr_documents');

    // Contract Management
    Route::get('/contracts',HrPage::class)
        ->defaults('component', 'hr.hr-contracts')
        ->name('contracts.index')
        ->middleware('permission:manage_contracts');
    Route::get('/contracts/{contract}/delete',HrPage::class)
        ->defaults('component', 'hr.hr-contracts.delete')
        ->name('contracts.delete')
        ->middleware('permission:delete_contracts');
    
    // Onboarding Management
    Route::get('/onboarding',HrPage::class)
        ->defaults('component', 'hr.hr-onboarding')
        ->name('onboarding')
        ->middleware('permission:manage_onboarding');
    Route::get('/onboarding/create',HrPage::class)
        ->defaults('component', 'hr.hr-onboarding-create')
        ->name('onboarding.create')
        ->middleware('permission:create_onboarding');
    Route::get('/onboarding/{onboarding}',HrPage::class)
        ->defaults('component', 'hr.hr-onboarding-show')
        ->name('onboarding.show')
        ->middleware('permission:show_onboarding');
    Route::get('/onboarding/templates',HrPage::class)
        ->defaults('component', 'hr.hr-onboarding-templates')
        ->name('onboarding.templates')
        ->middleware('permission:manage_onboarding_templates');

    // Performance Management
    Route::get('/performance',HrPage::class)
        ->defaults('component', 'hr.hr-performance')
        ->name('performance.index')
        ->middleware('permission:show_hr_performance');

    // Attendance Management
    Route::get('/attendance',HrPage::class)
        ->defaults('component', 'hr.hr-attendance')
        ->name('attendance.index')
        ->middleware('permission:manage_hr_attendance');
});