<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Global\Page;

// Skills Management
Route::prefix('skills')->middleware(['auth', 'permission:access_skills_certifications_module'])->name('skills.')->group(function () {
    Route::get('/', Page::class)
        ->name('index')
        ->defaults('component', 'skills.skill-index')
        ->middleware('permission:manage_skills');

    Route::get('/create', Page::class)
        ->name('create')
        ->defaults('component', 'skills.skill-create')
        ->middleware('permission:create_skill');

    Route::get('/{skill}/edit', Page::class)
        ->name('edit')
        ->defaults('component', 'skills.skill-edit')
        ->middleware('permission:edit_skill');

    Route::get('/{skill}', Page::class)
        ->name('show')
        ->defaults('component', 'skills.skill-show')
        ->middleware('permission:show_skill');
});

// Certifications Management
Route::prefix('certifications')->middleware(['auth', 'permission:access_skills_certifications_module'])->name('certifications.')->group(function () {
    Route::get('/', Page::class)
        ->name('index')
        ->defaults('component', 'certifications.certification-index')
        ->middleware('permission:manage_certifications');
    Route::get('/create', Page::class)
        ->name('create')
        ->defaults('component', 'certifications.certification-create')
        ->middleware('permission:create_certification');
    Route::get('/{certification}/edit', Page::class)
        ->name('edit')
        ->defaults('component', 'certifications.certification-edit')
        ->middleware('permission:edit_certification');
    Route::get('/{certification}', Page::class)
        ->name('show')
        ->defaults('component', 'certifications.certification-show')
        ->middleware('permission:show_certification');
});