@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Permission System Examples</h1>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Using Blade Directives</h2>
        </div>
        <div class="card-body">
            <h3>@permission Directive</h3>
            @permission('view_users')
                <p>This content is only visible to users with the 'view_users' permission.</p>
            @endpermission
            
            <h3>@module Directive</h3>
            @module('users')
                <p>This content is only visible to users with access to the 'users' module.</p>
            @endmodule
            
            <h3>@role Directive</h3>
            @role('admin')
                <p>This content is only visible to users with the 'admin' role.</p>
            @endrole
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Using Blade Components</h2>
        </div>
        <div class="card-body">
            <h3>Can Component</h3>
            <x-can permission="edit_users">
                <p>This content is only visible to users who can edit users.</p>
                
                <!-- Nested permissions (requires all) -->
                <x-can :permission="['delete_users', 'manage_users']" :require-all="true">
                    <p>This requires both 'delete_users' AND 'manage_users' permissions.</p>
                </x-can>
            </x-can>
            
            <h3>Module Component</h3>
            <x-module module="reports">
                <p>This content is only visible to users with access to the 'reports' module.</p>
            </x-module>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Using Helper Functions</h2>
        </div>
        <div class="card-body">
            <h3>has_any_permission()</h3>
            @if(has_any_permission(['view_users', 'edit_users']))
                <p>This content is visible if the user has either 'view_users' OR 'edit_users' permission.</p>
            @endif
            
            <h3>has_all_permissions()</h3>
            @if(has_all_permissions(['create_users', 'edit_users']))
                <p>This content is only visible if the user has BOTH 'create_users' AND 'edit_users' permissions.</p>
            @endif
            
            <h3>module_route()</h3>
            @php
                $userProfileRoute = module_route('users.profile', ['id' => 1]);
            @endphp
            @if($userProfileRoute)
                <a href="{{ $userProfileRoute }}" class="btn btn-primary">View User Profile</a>
            @endif
            
            <h3>module_link()</h3>
            {!! module_link('users.index', 'View All Users', [], ['class' => 'btn btn-info']) !!}
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h2>Conditional Buttons</h2>
        </div>
        <div class="card-body">
            <div class="mb-3">
                @if(has_any_permission('create_users'))
                    <a href="{{ route('users.create') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New User
                    </a>
                @endif
                
                @if(has_any_permission('export_users'))
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i> Export Users
                    </button>
                @endif
                
                @if(has_any_permission('import_users'))
                    <button class="btn btn-warning">
                        <i class="fas fa-upload"></i> Import Users
                    </button>
                @endif
            </div>
            
            <div class="alert alert-info">
                <h4>Note:</h4>
                <p>These examples demonstrate different ways to implement permission checks in your views. Choose the approach that best fits your needs.</p>
                <ul>
                    <li>Use <code>@permission</code> for simple permission checks</li>
                    <li>Use <code>@module</code> for module-based access control</li>
                    <li>Use <code>@role</code> for role-based access control</li>
                    <li>Use <code><x-can></code> and <code><x-module></code> components for more complex permission logic</li>
                    <li>Use helper functions for custom logic in your Blade templates</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
