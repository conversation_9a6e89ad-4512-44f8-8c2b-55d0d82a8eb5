<x-content>
    <x-card key="agent-index-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    @if(auth()->user()->role_id !== 6)
                        <div class="lg:pr-3">
                            <label for="campaigns-search" class="sr-only">Search</label>
                            <div x-data="{ open: false }" class="relative mt-1 lg:w-64 xl:w-96">
                                <input
                                    id="campaigns-search"
                                    type="text"
                                    x-on:focus="open = true"
                                    x-on:click.away="open = false"
                                    wire:model.live="campaignSearch"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Search for campaign"
                                />

                                <ul
                                    x-show="open"
                                    x-cloak
                                    class="absolute z-10 mt-1 w-full text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                >
                                    @forelse($filteredCampaigns as $campaign)
                                        <li
                                            wire:click="selectCampaign({{ $campaign->id }})"
                                            x-on:click="open = false"
                                            class="px-2 py-1 hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-700"
                                        >
                                            {{ $campaign->name }}
                                        </li>
                                    @empty
                                        <li class="w-full p-4 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Aucun agent trouvé.</li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                    @else
                        <div class="lg:pr-3">
                            <h3 class="flex items-center text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $selectedCampaign->name }}
                            </h3>
                        </div>
                    @endif
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                          </svg>
                        </div>
                        <input
                              type="date"
                              wire:model.live="startDate"
                              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                              placeholder="From"
                          />
                      </div>
                      <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                          </svg>
                        </div>
                        <input
                              type="date"
                              wire:model.live="endDate"
                              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                              placeholder="To"
                          />
                      </div>
                      <select wire:model.live="period" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                          <option value="day">Daily</option>
                          <option value="week">Weekly</option>
                          <option value="month">Monthly</option>
                          <option value="year">Yearly</option>
                      </select>
                    <button wire:click="exportStatisticsData" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                        Export
                    </button>
                </div>
            </div>
        </div>
    </x-card>
    <x-content-body>
        <div class="mt-4">
            <div class="{{ $selectedCampaignId ? 'grid gap-4 xl:grid-cols-2 2xl:grid-cols-3' : ''}}">
                <!-- Main campaign statistics widget -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex-shrink-0">
                      @if($selectedCampaign && isset($statisticsData['total_appointments']))
                        <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white">{{ $statisticsData['total_appointments'] }}</span>
                        <h3 class="text-base font-light text-gray-500 dark:text-gray-400">Total Appointments</h3>
                      @else
                        <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white">0</span>
                        <h3 class="text-base font-light text-gray-500 dark:text-gray-400">No data available</h3>
                      @endif
                    </div>
                    <div class="flex items-center justify-end flex-1 text-base font-medium">
                      @if($selectedCampaign && isset($statisticsData['validation_rate']))
                        <span class="{{ $statisticsData['validation_rate'] >= 70 ? 'text-green-500 dark:text-green-400' : ($statisticsData['validation_rate'] >= 50 ? 'text-yellow-500 dark:text-yellow-400' : 'text-red-500 dark:text-red-400') }}">
                          {{ $statisticsData['validation_rate'] }}%
                          <span class="ml-2 text-gray-500 dark:text-gray-400">Validation Rate</span>
                        </span>
                      @endif
                    </div>
                  </div>

                  <!-- Campaign metrics cards -->
                  <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2 lg:grid-cols-4">
                    <!-- Total Agents Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Agents</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $statisticsData['total_agents'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900">
                          <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v1h-3zM4.75 12.094A5.973 5.973 0 004 15v1H1v-1a3 3 0 013.75-2.906z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Validated Appointments Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Validated</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $statisticsData['validated_appointments'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900">
                          <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Rejected Appointments Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Rejected</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $statisticsData['rejected_appointments'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 dark:bg-red-900">
                          <svg class="w-6 h-6 text-red-600 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Daily Average Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Daily Avg</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $statisticsData['daily_average'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900">
                          <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Campaign Performance Chart -->
                  <div id="campaign-performance-chart" class="w-full h-80" data-chart="{{ json_encode($chartData) }}"></div>

                  <script src="{{ asset('js/campaign-performance-chart.js') }}"></script>

                  <!-- Card Footer -->
                  <div class="flex items-center justify-between pt-3 mt-4 border-t border-gray-200 sm:pt-6 dark:border-gray-700">
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Period: {{ ucfirst($period) }}
                      </span>
                    </div>
                    <div class="flex-shrink-0">
                      <div class="inline-flex items-center p-2 text-xs font-medium uppercase rounded-lg text-primary-700 sm:text-sm dark:text-primary-500">
                        <span class="mr-2">Campaign Manager:</span>
                        <span class="font-bold">{{ $selectedCampaign->manager->first_name ?? 'None' }} {{ $selectedCampaign->manager->last_name ?? '' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                @if($selectedCampaignId)
                <!-- Agent Performance Table -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                  <h3 class="flex items-center mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    Agent Performance
                    <button data-popover-target="popover-description" data-popover-placement="bottom-end" type="button">
                      <svg class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="sr-only">Show information</span>
                    </button>
                  </h3>
                  <div data-popover id="popover-description" role="tooltip" class="absolute z-10 invisible inline-block text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
                    <div class="p-3 space-y-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Agent Performance</h3>
                        <p>View detailed performance metrics for each agent in this campaign.</p>
                    </div>
                    <div data-popper-arrow></div>
                  </div>

                  @if($selectedCampaign && !empty($agentPerformance))
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Agent</th>
                                    <th scope="col" class="px-4 py-3">Appointments</th>
                                    <th scope="col" class="px-4 py-3">Validation Rate</th>
                                    <th scope="col" class="px-4 py-3">Daily Average</th>
                                    <th scope="col" class="px-4 py-3">Hours</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agentPerformance as $agent)
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            {{ $agent['name'] }}
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <span class="mr-2">{{ $agent['total_appointments'] }}</span>
                                                <span class="text-xs text-green-600 dark:text-green-400">({{ $agent['validated_appointments'] }} validated)</span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                                    <div class="h-2.5 rounded-full {{ $agent['validation_rate'] >= 70 ? 'bg-green-600' : ($agent['validation_rate'] >= 50 ? 'bg-yellow-400' : 'bg-red-600') }}" style="width: {{ $agent['validation_rate'] }}%"></div>
                                                </div>
                                                <span>{{ $agent['validation_rate'] }}%</span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            {{ $agent['daily_average'] }}
                                        </td>
                                        <td class="px-4 py-3">
                                            {{ $agent['total_hours'] }} ({{ $agent['shifts_count'] }} shifts)
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                  @else
                    <div class="flex items-center justify-center h-40">
                        <p class="text-gray-500 dark:text-gray-400">No agent performance data available for this campaign.</p>
                    </div>
                  @endif
                </div>
                @endif
            </div>
        </div>
    </x-content-body>
</x-content>
