<x-app-layout>
    <x-page-header :title="$current_page_resume['title']" :description="$current_page_resume['description']" :icon="$current_page_resume['icon']" />

    <x-page-sidebar :pages="$pages" :current_page="$current_page" :section="$current_page_section" />

    <x-page-content>
        @if ($current_page['component'] === 'call-quality.dashboard')
            <livewire:call-quality.dashboard />
        @elseif ($current_page['component'] === 'call-quality.call-index')
            <livewire:call-quality.call-index />
        @elseif ($current_page['component'] === 'call-quality.evaluation-index')
            <livewire:call-quality.evaluation-index />
        @elseif ($current_page['component'] === 'call-quality.criteria-index')
            <livewire:call-quality.criteria-index />
        @elseif ($current_page['component'] === 'call-quality.quality-reports')
            <livewire:call-quality.quality-reports />
        @elseif ($current_page['component'] === 'call-quality.quality-settings')
            <livewire:call-quality.quality-settings />
        @endif
    </x-page-content>
</x-app-layout>
