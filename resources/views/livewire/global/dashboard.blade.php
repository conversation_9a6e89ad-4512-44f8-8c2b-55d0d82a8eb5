<x-content class="dashboard-page">
    {{-- ================================================================= --}}
    {{-- SECTION: DASHBOARD HEADER & PERIOD SELECTOR                     --}}
    {{-- ================================================================= --}}
    <div class="mb-4 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">Dashboard</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Overview of your business performance and metrics</p>
        </div>

        <div class="flex items-center gap-2">
            <button wire:click="refreshData" class="inline-flex items-center p-2 text-sm font-medium rounded-lg text-primary-700 hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700" title="Refresh data">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 {{ $isLoading ? 'animate-spin' : '' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="sr-only">Refresh</span>
            </button>
            <div class="flex items-center space-x-2">
                <label for="period-select" class="text-sm font-medium text-gray-900 dark:text-white">Period:</label>
                <select wire:model.live="period" id="period-select" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="day">Today</option>
                    <option value="week">Last 7 days</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>
            </div>
        </div>
    </div>

    {{-- ================================================================= --}}
    {{-- SECTION: QUICK STATS CARDS                                     --}}
    {{-- Displays key metrics in card format (appointments, campaigns)  --}}
    {{-- ================================================================= --}}
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-{{ auth()->user()->hasRole(['admin', 'director', 'manager']) ? '4' : (auth()->user()->hasRole(['supervisor', 'team_lead']) ? '3' : '2') }} gap-4 mb-6">
        @can('show_dashboard_appointment_stats')
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5 flex items-center">
                <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pending Appointments</dt>
                        <dd class="flex items-center">
                            <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $quickStats['pending_appointments'] }}</div>
                            <a href="{{ route('appointments.index') }}" class="ml-2 text-sm text-primary-600 dark:text-primary-500 hover:underline">View</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        @endcan

        @can('show_dashboard_campaign_stats')
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5 flex items-center">
                <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Active Campaigns</dt>
                        <dd class="flex items-center">
                            <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $quickStats['active_campaigns'] }}</div>
                            <a href="{{ route('campaigns.index') }}" class="ml-2 text-sm text-primary-600 dark:text-primary-500 hover:underline">View</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        @endcan

        @can('show_dashboard_agent_stats')
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5 flex items-center">
                <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Agents in Training</dt>
                        <dd class="flex items-center">
                            <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $quickStats['agents_in_training'] }}</div>
                            <a href="{{ route('agents.index') }}" class="ml-2 text-sm text-primary-600 dark:text-primary-500 hover:underline">View</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        @endcan

        @can('show_dashboard_report_stats')
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-5 flex items-center">
                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                    <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Unread Reports</dt>
                        <dd class="flex items-center">
                            <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $quickStats['unread_reports'] }}</div>
                            <a href="{{ route('reports.index') }}" class="ml-2 text-sm text-primary-600 dark:text-primary-500 hover:underline">View</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        @endcan
    </div>

    {{-- ================================================================= --}}
    {{-- SECTION: MAIN STATISTICAL CHARTS                              --}}
    {{-- Shows user, agent, and campaign growth trends                 --}}
    {{-- ================================================================= --}}
    <!-- Main Stats -->
    @if(auth()->user()->hasAnyRole(['admin', 'director', 'manager', 'supervisor']) || 
        auth()->user()->hasAnyPermission(['show_dashboard_user_stats', 'show_dashboard_agent_stats', 'show_dashboard_campaign_stats']))
    <div class="mb-6 grid w-full grid-cols-1 gap-4 xl:grid-cols-{{ 
    (auth()->user()->hasPermissionTo('show_dashboard_user_stats') && isset($userData) && 
     auth()->user()->hasPermissionTo('show_dashboard_agent_stats') && isset($agentData) && 
     auth()->user()->hasPermissionTo('show_dashboard_campaign_stats') && isset($campaignData)) ? '3' : 
    ((auth()->user()->hasPermissionTo('show_dashboard_user_stats') && isset($userData) && 
      auth()->user()->hasPermissionTo('show_dashboard_agent_stats') && isset($agentData)) || 
     (auth()->user()->hasPermissionTo('show_dashboard_agent_stats') && isset($agentData) && 
      auth()->user()->hasPermissionTo('show_dashboard_campaign_stats') && isset($campaignData)) || 
     (auth()->user()->hasPermissionTo('show_dashboard_user_stats') && isset($userData) && 
      auth()->user()->hasPermissionTo('show_dashboard_campaign_stats') && isset($campaignData)) ? '2' : '1') 
    }}">
        @can('show_dashboard_user_stats')
        @if($userData !== null)
        <x-card key="users-chart" class="items-center justify-between px-4 py-4 sm:px-6">
            <div class="flex w-full items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $userData['title'] ?? 'Users' }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $userData['current_period'] ?? 'Current Period' }}</p>
                </div>
                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                    {{ $userData['count'] ?? 0 }}
                    <span class="ml-2 rounded-full px-2.5 py-0.5 text-xs font-medium {{ $userData['growth_percentage'] > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                        {{ $userData['growth_percentage'] > 0 ? '+' : '' }}{{ $userData['growth_percentage'] ?? 0 }}%
                    </span>
                </div>
            </div>
            <div id="users-chart-container" class="w-full" data-chart='{{ json_encode($userData["chart_data"] ?? ['series' => [['name' => 'Users', 'data' => [0, 0, 0, 0, 0, 0, 0]]], 'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']]) }}'></div>
        </x-card>
        @endif
        @endcan
        
        @can('show_dashboard_agent_stats')
        @if($agentData !== null)
        <x-card key="agents-chart" class="items-center justify-between px-4 py-4 sm:px-6">
            <div class="flex w-full items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $agentData['title'] ?? 'Agents' }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $agentData['current_period'] ?? 'Current Period' }}</p>
                </div>
                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                    {{ $agentData['count'] ?? 0 }}
                    <span class="ml-2 rounded-full px-2.5 py-0.5 text-xs font-medium {{ $agentData['growth_percentage'] > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                        {{ $agentData['growth_percentage'] > 0 ? '+' : '' }}{{ $agentData['growth_percentage'] ?? 0 }}%
                    </span>
                </div>
            </div>
            <div id="agents-chart-container" class="w-full" data-chart='{{ json_encode($agentData["chart_data"]) }}'></div>
        </x-card>
        @endif
        @endcan
        
        @can('show_dashboard_campaign_stats')
        @if($campaignData !== null)
        <x-card key="campaigns-chart" class="items-center justify-between px-4 py-4 sm:px-6">
            <div class="flex w-full items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $campaignData['title'] ?? 'Campaigns' }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $campaignData['current_period'] ?? 'Current Period' }}</p>
                </div>
                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                    {{ $campaignData['count'] ?? 0 }}
                    <span class="ml-2 rounded-full px-2.5 py-0.5 text-xs font-medium {{ $campaignData['growth_percentage'] > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                        {{ $campaignData['growth_percentage'] > 0 ? '+' : '' }}{{ $campaignData['growth_percentage'] ?? 0 }}%
                    </span>
                </div>
            </div>
            <div id="campaigns-chart-container" class="w-full" data-chart='{{ json_encode($campaignData["chart_data"] ?? ['series' => [['name' => 'Campaigns', 'data' => [0, 0, 0, 0, 0, 0, 0]]], 'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']]) }}'></div>
        </x-card>
        @endif
        @endcan
    </div>
    @endif

    {{-- ================================================================= --}}
    {{-- SECTION: DISTRIBUTION PIE CHARTS                              --}}
    {{-- Shows breakdowns of users, agents, campaigns & appointments   --}}
    {{-- ================================================================= --}}
    @if(auth()->user()->hasAnyRole(['admin', 'director', 'manager', 'supervisor', 'team_lead', 'quality_control']) || 
        auth()->user()->hasAnyPermission(['view_user_stats', 'view_agent_stats', 'view_campaign_stats', 'view_appointment_stats']))
    <div class="grid w-full grid-cols-1 gap-4 xl:grid-cols-2 2xl:grid-cols-{{ 
        auth()->user()->hasPermissionTo('show_dashboard_user_stats') && 
        auth()->user()->hasPermissionTo('show_dashboard_agent_stats') && 
        auth()->user()->hasPermissionTo('show_dashboard_campaign_stats') && 
        auth()->user()->hasPermissionTo('show_dashboard_appointment_stats') ? '4' : 
        ((auth()->user()->hasAnyPermission(['view_user_stats', 'view_agent_stats', 'view_campaign_stats', 'view_appointment_stats']) ? '3' : '2')) 
    }}">
        @can('show_dashboard_user_stats')
        @if(isset($userData) && isset($userData['role_distribution']) && is_array($userData['role_distribution']) && count($userData['role_distribution']) > 0)
        <x-card key="pie-chart-1" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">User Distribution</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Distribution by user role</span>
                </div>
            </div>
            <div id="user-role-chart" class="w-full h-72"
                data-labels="{{ json_encode(array_keys($userData['role_distribution'])) }}"
                data-values="{{ json_encode(array_values($userData['role_distribution'])) }}">
            </div>
        </x-card>
        @endif
        @endcan

        @can('show_dashboard_agent_stats')
        @if(isset($agentData) && isset($agentData['formation_status']) && is_array($agentData['formation_status']) && count($agentData['formation_status']) > 0)
        <x-card key="pie-chart-2" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Agent Formation</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Distribution by training status</span>
                </div>
            </div>
            <div id="agent-formation-chart" class="w-full h-72"
                data-labels="{{ json_encode(array_keys($agentData['formation_status'])) }}"
                data-values="{{ json_encode(array_values($agentData['formation_status'])) }}">
            </div>
        </x-card>
        @endif
        @endcan

        @can('show_dashboard_campaign_stats')
        @if(isset($campaignData) && isset($campaignData['status_distribution']) && is_array($campaignData['status_distribution']) && count($campaignData['status_distribution']) > 0)
        <x-card key="pie-chart-3" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Campaign Status</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Distribution by campaign state</span>
                </div>
            </div>
            <div id="campaign-status-chart" class="w-full h-72"
                data-labels="{{ json_encode(array_keys($campaignData['status_distribution'])) }}"
                data-values="{{ json_encode(array_values($campaignData['status_distribution'])) }}">
            </div>
        </x-card>
        @endif
        @endcan

        @can('show_dashboard_appointment_stats')
        @if(isset($appointmentData) && isset($appointmentData['status_distribution']) && is_array($appointmentData['status_distribution']) && count($appointmentData['status_distribution']) > 0)
        <x-card key="pie-chart-4" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Appointment Status</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Distribution by appointment state</span>
                </div>
            </div>
            <div id="appointment-status-chart" class="w-full h-72"
                data-labels="{{ json_encode(array_keys($appointmentData['status_distribution'])) }}"
                data-values="{{ json_encode(array_values($appointmentData['status_distribution'])) }}">
            </div>
        </x-card>
        @endif
        @endcan
    </div>
    @endif

    {{-- ================================================================= --}}
    {{-- SECTION: PERFORMANCE TREND CHARTS                             --}}
    {{-- Line charts showing performance metrics over time             --}}
    {{-- ================================================================= --}}
    <!-- Line charts for trends -->
    @if(auth()->user()->hasAnyRole(['admin', 'director', 'manager', 'supervisor', 'team_lead']) || 
        auth()->user()->hasAnyPermission(['view_agent_stats', 'view_campaign_stats']))
    <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-{{ 
        (auth()->user()->hasPermissionTo('show_dashboard_agent_stats') && 
         auth()->user()->hasPermissionTo('show_dashboard_campaign_stats')) ? '2' : '1' 
    }}">
        @can('show_dashboard_agent_stats')
        @if(isset($agentData) && isset($agentData['performance_metrics']))
        <x-card key="line-chart-1" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Agent Performance Metrics</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Calls, Appointments & Conversion Rate</span>
                </div>
                <div class="flex items-center justify-end">
                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                        <span class="inline-block w-3 h-3 mr-1 bg-blue-600 rounded-full"></span>
                        Calls
                    </div>
                    <div class="inline-flex items-center ml-3 text-base font-semibold text-gray-900 dark:text-white">
                        <span class="inline-block w-3 h-3 mr-1 bg-green-500 rounded-full"></span>
                        Appointments
                    </div>
                    <div class="inline-flex items-center ml-3 text-base font-semibold text-gray-900 dark:text-white">
                        <span class="inline-block w-3 h-3 mr-1 bg-yellow-300 rounded-full"></span>
                        Conversion %
                    </div>
                </div>
            </div>
            <div id="agent-performance-chart" class="w-full h-96">
                <!-- Chart will be rendered here -->
            </div>
        </x-card>
        @endif
        @endcan

        @can('show_dashboard_campaign_stats')
        @if(isset($campaignData) && isset($campaignData['monthly_campaigns']) && is_array($campaignData['monthly_campaigns']) && count($campaignData['monthly_campaigns']) > 0)
        <x-card key="line-chart-2" class="p-4 sm:p-6">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Campaign Metrics</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Monthly campaigns distribution</span>
                </div>
            </div>
            <div id="monthly-campaigns-chart" class="w-full h-96"
                data-labels="{{ json_encode(array_map(function($month) { return date('M', mktime(0, 0, 0, (int)$month, 1)); }, array_keys($campaignData['monthly_campaigns']))) }}"
                data-values="{{ json_encode(array_values($campaignData['monthly_campaigns'])) }}">
                <!-- Chart will be rendered here -->
            </div>
        </x-card>
        @endif
        @endcan
    </div>
    @endif

    {{-- ================================================================= --}}
    {{-- SECTION: CAMPAIGN & CALL CENTER METRICS                       --}}
    {{-- Detailed metrics on campaign performance and call statistics  --}}
    {{-- ================================================================= --}}
    @can('show_dashboard_campaign_stats')
    @if($campaignData !== null)
    <div class="mt-4 grid gap-4 xl:grid-cols-2 2xl:grid-cols-3">
        <!-- Main widget -->
        <x-card key="main-chart" class="p-4 sm:p-6 2xl:col-span-2">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Campaign Performance Metrics</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Revenue, Appointments & Conversion by Campaign</span>
                </div>
                <div class="flex items-center justify-end">
                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                        {{ $period }}
                    </div>
                </div>
            </div>
            @if(isset($campaignData['monthly_campaigns']) && count($campaignData['monthly_campaigns']) > 0)
                <div id="campaign-performance-chart" class="w-full h-80"
                    data-chart="{{ json_encode([
                        'series' => [
                            [
                                'name' => 'Revenue ($)',
                                'data' => isset($campaignData['performance']['revenue']) ? $campaignData['performance']['revenue'] : [12500, 15000, 8500, 14200, 9800, 11500]
                            ],
                            [
                                'name' => 'Appointments',
                                'data' => isset($campaignData['performance']['appointments']) ? $campaignData['performance']['appointments'] : [125, 150, 85, 142, 98, 115]
                            ],
                            [
                                'name' => 'Conversion Rate (%)',
                                'data' => isset($campaignData['performance']['conversion']) ? $campaignData['performance']['conversion'] : [8.2, 7.5, 6.8, 9.1, 7.2, 8.5]
                            ]
                        ],
                        'categories' => isset($campaignData['performance']['campaigns']) ? $campaignData['performance']['campaigns'] : ['Campaign A', 'Campaign B', 'Campaign C', 'Campaign D', 'Campaign E', 'Campaign F']
                    ]) }}">
                </div>
            @else
                <div class="flex items-center justify-center h-64">
                    <p class="text-gray-500 dark:text-gray-400">No monthly campaign data available</p>
                </div>
            @endif
        </x-card>

        <!--Tabs widget -->
        <x-widget-tab :data="['agentData' => $agentData, 'campaignData' => $campaignData]" />
    </div>
    @endif
    @endcan

    {{-- ================================================================= --}}
    {{-- SECTION: TOP PERFORMERS & RECENT ACTIVITIES                   --}}
    {{-- ================================================================= --}}
    <div class="grid grid-cols-1 my-4 xl:grid-cols-2 xl:gap-4">
        <!-- Campaign performers card -->
        @can('show_dashboard_campaign_stats')
        @if(isset($campaignData) && isset($campaignData['top_performers']) && is_array($campaignData['top_performers']))
        <x-card key="top-campaigns" class="p-4 sm:p-6 mb-4 xl:mb-0">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Top Performing Campaigns</h3>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Based on conversion rate</span>
                </div>
            </div>
            <div class="flow-root">
                <ul role="list" class="-my-2 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($campaignData['top_performers'] as $campaign)
                        <li class="py-3 sm:py-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate dark:text-white">{{ $campaign['name'] }}</p>
                                </div>
                                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                    {{ $campaign['performance'] }}%
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </x-card>
        @endif
        @endcan

        <!-- Activity Card -->
        @if(auth()->user()->hasAnyRole(['admin', 'director', 'manager']) || auth()->user()->can('show_dashboard_activities'))
        <x-card key="activity-chart-1" class="p-4 sm:p-6 mb-4 xl:mb-0">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
                <a href="#" class="inline-flex items-center p-2 text-sm font-medium rounded-lg text-primary-700 hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
                    View all
                </a>
            </div>
            <div>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($recentActivities as $activity)
                        <li class="py-3 sm:py-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate dark:text-white">
                                        {{ $activity['title'] }}
                                    </p>
                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                        {{ $activity['description'] }}
                                    </p>
                                    <span class="inline-flex items-center text-xs font-normal text-gray-500 dark:text-gray-400">
                                        <svg class="w-3 h-3 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
                                        </svg>
                                        {{ $activity['date'] }}
                                    </span>
                                </div>
                                <a href="{{ $activity['link'] }}" class="inline-flex items-center text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                    {{ $activity['link_text'] }}
                                </a>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </x-card>
        @endif

        <!-- Recent Transactions -->
        @if(auth()->user()->hasAnyRole(['admin', 'director', 'manager']) || auth()->user()->can('show_dashboard_transactions'))
        <x-card key="table-chart-1" class="p-4 sm:p-6 mb-4 xl:mb-0">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Transactions</h3>
                <a href="#" class="inline-flex items-center p-2 text-sm font-medium rounded-lg text-primary-700 hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
                    View all
                </a>
            </div>
            <div class="overflow-x-auto">
                <x-table-widget />
            </div>
        </x-card>
        @endif
    </div>

    {{-- ================================================================= --}}
    {{-- SECTION: JAVASCRIPT & CHART INITIALIZATION                    --}}
    {{-- Scripts for initializing and controlling dashboard charts      --}}
    {{-- ================================================================= --}}
    @push('scripts')
    <script src="{{ asset('js/dashboard-charts.js') }}?v={{ time() }}"></script>
    <script>
        // Additional script to ensure charts fit properly
        document.addEventListener('DOMContentLoaded', function() {
            // Function to fix chart sizing
            function fixChartSizing() {
                const chartContainers = document.querySelectorAll('#daily-appointments-chart, #daily-agent-activity-chart');

                chartContainers.forEach(container => {
                    // Ensure the container has the correct styles
                    container.style.width = '100%';
                    container.style.height = '100%';
                    container.style.maxWidth = '100%';

                    // Find the ApexCharts canvas within this container
                    const canvas = container.querySelector('.apexcharts-canvas');
                    if (canvas) {
                        canvas.style.maxWidth = '100%';
                        canvas.style.maxHeight = '100%';
                    }
                });
            }
            // Run the fix after charts are initialized
            setTimeout(fixChartSizing, 1000);

            // Also run when window is resized
            window.addEventListener('resize', fixChartSizing);
        });
    </script>
    @endpush
</x-content>
