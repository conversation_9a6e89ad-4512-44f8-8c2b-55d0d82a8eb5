<?php

namespace App\Livewire\Settings;

use Livewire\Component;
use App\Models\Role;
use App\Services\PermissionService;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Services\PermissionCacheService;
use Spatie\Permission\Models\Permission;

class RolePermissions extends Component
{
    use WithPagination;

    public $selectedRole = null;
    public $roles = [];
    public $permissions = [];
    public $selectedPermissions = [];
    public $searchTerm = '';
    public $groupedPermissions = [];
    public $permissionGroups = [];

    protected $permissionService;
    protected $permissionCacheService;

    public function __construct()
    {
        $this->permissionService = app(PermissionService::class);
        $this->permissionCacheService = app(PermissionCacheService::class);
    }

    public function mount()
    {
        $this->roles = Role::orderBy('id')->get();
        if ($this->roles->count() > 0) {
            $this->selectedRole = $this->roles->first()->id;
            $this->loadPermissions();
        }
    }

    public function loadPermissions()
    {
        if (!$this->selectedRole) {
            return;
        }

        $role = Role::find($this->selectedRole);
        if (!$role) {
            return;
        }

        // Clear any existing permission cache
        Cache::forget('role_permissions_' . $role->id);
        
        // Force reload of role permissions from database
        $role->load('permissions');
        
        // Get all permissions
        $this->permissions = Permission::orderBy('name')->get();
        
        // Get the role's permissions using fresh data
        $rolePermissions = $role->permissions()->get();
        
        // Log detailed information about permissions
        Log::info('Role permissions query result: ' . json_encode($rolePermissions->toArray()));
        
        // Get the IDs as an array
        $rolePermissionIds = $this->permissionService->getPermissionIds($rolePermissions);
        
        // Log the final selected permissions
        Log::info('Final selected permission IDs: ' . json_encode($rolePermissionIds));
        
        // Handle admin role (id = 1) - admin has all permissions
        if ($role->id === 1) {
            $this->selectedPermissions = $this->permissions->pluck('id')->toArray();
        } else {
            $this->selectedPermissions = $rolePermissionIds;
        }

        // Log the final state of selectedPermissions
        Log::info('Final selectedPermissions array: ' . json_encode($this->selectedPermissions));
        
        // Group permissions by category
        $this->groupPermissions();
        
        // Force refresh the component to ensure UI updates
        $this->dispatch('refresh');
    }

    public function groupPermissions()
    {
        $permissionData = $this->permissionService->getGroupedPermissions();
        
        $this->permissions = $permissionData['permissions'];
        $this->groupedPermissions = $permissionData['groupedPermissions'];
        $this->permissionGroups = $permissionData['permissionGroups'];
    }

    public function updatedSelectedRole()
    {
        // Reset selected permissions before loading new ones
        $this->selectedPermissions = [];
        $this->loadPermissions();
    }

    public function togglePermission($permissionId)
    {
        if (in_array($permissionId, $this->selectedPermissions)) {
            $this->selectedPermissions = array_diff($this->selectedPermissions, [$permissionId]);
        } else {
            $this->selectedPermissions[] = $permissionId;
        }
    }

    public function toggleAllInGroup($group)
    {
        if (!isset($this->groupedPermissions[$group])) {
            return;
        }

        $groupPermissionIds = collect($this->groupedPermissions[$group])->pluck('id')->toArray();
        
        // Check if all permissions in this group are already selected
        $allSelected = count(array_intersect($groupPermissionIds, $this->selectedPermissions)) === count($groupPermissionIds);
        
        if ($allSelected) {
            // Remove all permissions in this group
            $this->selectedPermissions = array_diff($this->selectedPermissions, $groupPermissionIds);
        } else {
            // Add all permissions in this group
            $this->selectedPermissions = array_unique(array_merge($this->selectedPermissions, $groupPermissionIds));
        }
    }

    public function savePermissions()
    {
        if (!$this->selectedRole) {
            session()->flash('error', 'No role selected.');
            return;
        }

        $role = Role::find($this->selectedRole);
        if (!$role) {
            session()->flash('error', 'Role not found.');
            return;
        }

        try {
            Log::info('Updating permissions for role: ' . $role->name);
            Log::info('Selected permissions: ' . json_encode($this->selectedPermissions));
            
            // Clear existing permissions first
            $role->permissions()->detach();
            Log::info('Cleared existing permissions for role');
            
            // Sync permissions
            $role->permissions()->sync($this->selectedPermissions);
            Log::info('Synced permissions for role');
            
            // Clear permission cache for users with this role
            $users = $role->users;
            Log::info('Found ' . $users->count() . ' users with this role');
            
            foreach ($users as $user) {
                Log::info('Processing user: ' . $user->name);
                $this->permissionCacheService->clearUserPermissionsCache($user);
                Log::info('Cleared cache for user: ' . $user->name);
                
                // Force refresh user's permissions
                $user->load('permissions');
                $user->load('role.permissions');
                $user->clearPermissionCache();
                
                // Refresh the user's permissions in the database
                $user->save();
                
                Log::info('Reloaded permissions for user: ' . $user->name);
                
                // Log the user's permissions
                Log::info('User ' . $user->name . ' permissions: ' . json_encode($user->getAllPermissions()));
            }
            
            // Clear the role's permission cache
            Cache::forget('role_permissions_' . $role->id);
            Log::info('Cleared role permissions cache');
            
            // Reload permissions
            $role->load('permissions');
            Log::info('Reloaded role permissions');
            
            session()->flash('success', 'Permissions updated successfully for role: ' . $role->name);
            
            // Force refresh the component to ensure UI updates
            $this->dispatch('refresh');
        } catch (\Exception $e) {
            Log::error('Error updating permissions: ' . $e->getMessage());
            session()->flash('error', 'Failed to update permissions: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.settings.role-permissions');
    }
}
