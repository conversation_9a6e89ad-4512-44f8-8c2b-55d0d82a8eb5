<?php

namespace App\Livewire\Settings;

use App\Livewire\Global\Page;
use App\Traits\HandlePageExpiration;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class SettingsPage extends Page
{
    use HandlePageExpiration;

    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = [];
    public array $current_page_section = [];

    #[On('to-settings-general')]
    public function toSettingsGeneral()
    {
        return $this->redirect(route('settings.general.index'), navigate: true);
    }

    #[On('to-settings-calls')]
    public function toSettingsCalls()
    {
        return $this->redirect(route('settings.calls'), navigate: true);
    }

    #[On('to-settings-campaigns')]
    public function toSettingsCampaigns()
    {
        return $this->redirect(route('settings.campaigns'), navigate: true);
    }

    #[On('to-settings-notifications')]
    public function toSettingsNotifications()
    {
        return $this->redirect(route('settings.notifications'), navigate: true);
    }

    #[On('to-settings-integrations')]
    public function toSettingsIntegrations()
    {
        return $this->redirect(route('settings.integrations'), navigate: true);
    }

    #[On('to-settings-departments')]
    public function toSettingsDepartments()
    {
        return $this->redirect(route('settings.departments'), navigate: true);
    }

    #[On('to-settings-permissions-roles')]
    public function toSettingsPermissionsRoles()
    {
        return $this->redirect(route('settings.permissions.roles'), navigate: true);
    }

    #[On('to-settings-permissions-users')]
    public function toSettingsPermissionsUsers()
    {
        return $this->redirect(route('settings.permissions.users'), navigate: true);
    }

    public function setPageResume($routeName)
    {
        switch ($routeName) {
            case 'settings.general.index':
                $this->current_page_resume['title'] = 'General Settings';
                $this->current_page_resume['description'] = 'Manage general call center settings';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'cog';
                break;
            case 'settings.calls':
                $this->current_page_resume['title'] = 'Call Settings';
                $this->current_page_resume['description'] = 'Configure call handling and routing preferences';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'phone';
                break;
            case 'settings.campaigns':
                $this->current_page_resume['title'] = 'Campaign Settings';
                $this->current_page_resume['description'] = 'Manage campaign configurations and defaults';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'speakerphone';
                break;
            case 'settings.notifications':
                $this->current_page_resume['title'] = 'Notification Settings';
                $this->current_page_resume['description'] = 'Configure system and user notifications';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'bell';
                break;
            case 'settings.integrations':
                $this->current_page_resume['title'] = 'Integrations';
                $this->current_page_resume['description'] = 'Manage third-party service integrations';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'link';
                break;
            case 'settings.departments':
                $this->current_page_resume['title'] = 'Department Management';
                $this->current_page_resume['description'] = 'Create and manage company departments';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'office-building';
                break;
            case 'settings.departments.create':
                 $this->current_page_resume['title'] = 'Department Management';
                $this->current_page_resume['description'] = 'Create and manage company departments';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'office-building';
                break;
            case 'settings.departments.edit':
                 $this->current_page_resume['title'] = 'Department Management';
                $this->current_page_resume['description'] = 'Create and manage company departments';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'office-building';
                break;
            case 'settings.departments.delete':
                $this->current_page_resume['title'] = 'Department Management';
                $this->current_page_resume['description'] = 'Create and manage company departments';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'office-building';
                break;
            case 'settings.permissions.roles':
                $this->current_page_resume['title'] = 'Role Permissions';
                $this->current_page_resume['description'] = 'Manage permissions for each role';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'shield-check';
                break;
            case 'settings.permissions.users':
                $this->current_page_resume['title'] = 'User Permissions';
                $this->current_page_resume['description'] = 'Manage direct permissions for users';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'shield-check';
                break;
            default:
                $this->current_page_resume['title'] = 'Settings';
                $this->current_page_resume['description'] = 'Manage call center settings and configurations';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'cog';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);

        $user = Auth::user();

        $this->pages = [
            [
                'module_id' => 'settings-general-module',
                'title' => 'General Settings',
                'description' => 'Configure general call center settings',
                'route' => 'settings.general.index',
                'display' => true,
                'authorized_permissions' => ['manage_settings'],
                'section_routes' => ['settings.general.index'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-calls-module',
                'title' => 'Call Settings',
                'description' => 'Configure call handling and routing preferences',
                'route' => 'settings.calls',
                'display' => true,
                'authorized_permissions' => ['manage_settings'],
                'section_routes' => ['settings.calls'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-campaigns-module',
                'title' => 'Campaign Settings',
                'description' => 'Manage campaign configurations and defaults',
                'route' => 'settings.campaigns',
                'display' => true,
                'authorized_permissions' => ['manage_settings'],
                'section_routes' => ['settings.campaigns'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-departments-module',
                'title' => 'Department Management',
                'description' => 'Create and manage company departments',
                'route' => 'settings.departments',
                'display' => true,
                'authorized_permissions' => ['manage_departments'],
                'section_routes' => ['settings.departments', 'settings.departments.create', 'settings.departments.edit', 'settings.departments.delete'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-notifications-module',
                'title' => 'Notification Settings',
                'description' => 'Configure system and user notifications',
                'route' => 'settings.notifications',
                'display' => true,
                'authorized_permissions' => ['manage_settings'],
                'section_routes' => ['settings.notifications'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-integrations-module',
                'title' => 'Integrations',
                'description' => 'Manage third-party service integrations',
                'route' => 'settings.integrations',
                'display' => true,
                'authorized_permissions' => ['manage_system_settings'],
                'section_routes' => ['settings.integrations'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-permissions-roles-module',
                'title' => 'Role Permissions',
                'description' => 'Manage permissions for each role',
                'route' => 'settings.permissions.roles',
                'display' => true,
                'authorized_permissions' => ['manage_permissions'],
                'section_routes' => ['settings.permissions.roles'],
                'sections' => []
            ],
            [
                'module_id' => 'settings-permissions-users-module',
                'title' => 'User Permissions',
                'description' => 'Manage direct permissions for users',
                'route' => 'settings.permissions.users',
                'display' => true,
                'authorized_permissions' => ['manage_permissions'],
                'section_routes' => ['settings.permissions.users'],
                'sections' => []
            ]
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    /**
     * Render the settings page view
     */
    public function render()
    {
        return view('livewire.settings.settings-page', [
            'pages' => $this->pages,
            'current_page' => $this->current_page,
            'current_page_resume' => $this->current_page_resume,
            'current_page_section' => $this->current_page_section,
        ]);
    }
}
