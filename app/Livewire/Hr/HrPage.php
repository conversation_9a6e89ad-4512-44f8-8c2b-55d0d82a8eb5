<?php

namespace App\Livewire\Hr;

use App\Livewire\Global\Page;
use App\Models\User;
use App\Models\Document;
use App\Models\Contract;
use App\Traits\HandlePageExpiration;
use Livewire\Attributes\On;

class HrPage extends Page
{
    use HandlePageExpiration;

    public ?User $employee = null;
    public ?Document $document = null;
    public ?Contract $contract = null;
    public $onboarding = null;
    public $onboardingTemplate = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-hr-index')]
    public function toHrIndex()
    {
        return $this->redirect(route('hr.index'), navigate: true);
    }

    #[On('to-hr-employees')]
    public function toHrEmployees()
    {
        return $this->redirect(route('hr.employees'), navigate: true);
    }

    #[On('to-employee-create')]
    public function toEmployeeCreate()
    {
        return $this->redirect(route('hr.employees.create'), navigate: true);
    }

    #[On('to-employee-show')]
    public function toEmployeeShow(User $employee)
    {
        if (!$employee) {
            return $this->redirect(route('hr.employees'), navigate: true);
        }
        return $this->redirect(route('hr.employees.show', ['employee' => $employee]), navigate: true);
    }

    #[On('to-employee-edit')]
    public function toEmployeeEdit(User $employee)
    {
        if (!$employee) {
            return $this->redirect(route('hr.employees'), navigate: true);
        }
        return $this->redirect(route('hr.employees.edit', ['employee' => $employee]), navigate: true);
    }

    #[On('to-employee-delete')]
    public function toEmployeeDelete(User $employee)
    {
        if (!$employee) {
            return $this->redirect(route('hr.employees'), navigate: true);
        }
        return $this->redirect(route('hr.employees.delete', ['employee' => $employee]), navigate: true);
    }

    #[On('to-hr-documents')]
    public function toHrDocuments()
    {
        return $this->redirect(route('hr.documents'), navigate: true);
    }

    #[On('to-hr-documents-create')]
    public function toHrDocumentsCreate()
    {
        return $this->redirect(route('hr.documents.create'), navigate: true);
    }

    #[On('to-hr-documents-show')]
    public function toHrDocumentsShow(Document $document)
    {
        if (!$document) {
            return $this->redirect(route('hr.documents'), navigate: true);
        }
        return $this->redirect(route('hr.documents.show', ['document' => $document]), navigate: true);
    }

    #[On('to-hr-documents-edit')]
    public function toHrDocumentsEdit(Document $document)
    {
        if (!$document) {
            return $this->redirect(route('hr.documents'), navigate: true);
        }
        return $this->redirect(route('hr.documents.edit', ['document' => $document]), navigate: true);
    }

    #[On('to-hr-documents-delete')]
    public function toHrDocumentsDelete(Document $document)
    {
        if (!$document) {
            return $this->redirect(route('hr.documents'), navigate: true);
        }
        return $this->redirect(route('hr.documents.delete', ['document' => $document]), navigate: true);
    }

    #[On('to-hr-contracts')]
    public function toHrContracts()
    {
        return $this->redirect(route('hr.contracts'), navigate: true);
    }

    #[On('to-contract-create')]
    public function toContractCreate()
    {
        return $this->redirect(route('hr.contracts.create'), navigate: true);
    }

    #[On('to-contract-show')]
    public function toContractShow(Contract $contract)
    {
        if (!$contract) {
            return $this->redirect(route('hr.contracts'), navigate: true);
        }
        return $this->redirect(route('hr.contracts.show', ['contract' => $contract]), navigate: true);
    }

    #[On('to-contract-edit')]
    public function toContractEdit(Contract $contract)
    {
        if (!$contract) {
            return $this->redirect(route('hr.contracts'), navigate: true);
        }
        return $this->redirect(route('hr.contracts.edit', ['contract' => $contract]), navigate: true);
    }

    #[On('to-contract-delete')]
    public function toContractDelete(Contract $contract)
    {
        if (!$contract) {
            return $this->redirect(route('hr.contracts'), navigate: true);
        }
        return $this->redirect(route('hr.contracts.delete', ['contract' => $contract]), navigate: true);
    }

    #[On('to-hr-performance')]
    public function toHrPerformance()
    {
        return $this->redirect(route('hr.performance'), navigate: true);
    }

    #[On('to-hr-attendance')]
    public function toHrAttendance()
    {
        return $this->redirect(route('hr.attendance'), navigate: true);
    }

    #[On('to-hr-onboarding')]
    public function toHrOnboarding()
    {
        return $this->redirect(route('hr.onboarding'), navigate: true);
    }

    #[On('to-hr-onboarding-create')]
    public function toHrOnboardingCreate()
    {
        return $this->redirect(route('hr.onboarding.create'), navigate: true);
    }

    #[On('to-hr-onboarding-show')]
    public function toHrOnboardingShow($onboardingId)
    {
        return $this->redirect(route('hr.onboarding.show', ['onboarding' => $onboardingId]), navigate: true);
    }

    #[On('to-hr-onboarding-templates')]
    public function toHrOnboardingTemplates()
    {
        return $this->redirect(route('hr.onboarding.templates'), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Human Resources';
        $this->resumeDescription = 'HR management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Human Resources',
            'description' => 'Comprehensive human resources management system for employee administration, document management, contract tracking, performance evaluation, and onboarding processes. This module allows you to manage all aspects of employee lifecycle from hiring to retirement.'
        ];

        switch ($routeName) {
            case 'hr.index':
                $totalEmployees = User::whereHas('roles')->count();
                $activeEmployees = User::where('status', 'actif')->count();
                $inactiveEmployees = $totalEmployees - $activeEmployees;
                $newEmployees = User::where('created_at', '>=', now()->subDays(30))->count();
                $departmentCount = \App\Models\Department::count();

                $this->resumeTitle = 'Human Resources';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Human Resources Dashboard',
                    'description' => 'Comprehensive overview of the organization\'s human resources. This dashboard provides key metrics and insights into workforce composition, employee status, departmental distribution, and recent hiring activity. Use this information to monitor HR operations and make strategic workforce decisions.',
                    'metrics' => [
                        ['label' => 'Total Employees', 'value' => $totalEmployees, 'change' => null],
                        ['label' => 'Active Employees', 'value' => $activeEmployees, 'change' => $totalEmployees > 0 ? round(($activeEmployees / $totalEmployees) * 100) : 0],
                        ['label' => 'New Hires', 'value' => $newEmployees, 'change' => null, 'description' => 'Last 30 days'],
                        ['label' => 'Departments', 'value' => $departmentCount, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Human Resources';
                $this->current_page_resume['description'] = 'HR Dashboard and Overview';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['icon'] = 'users';
                break;

            // Employee Management
            case 'hr.employees':
                $totalEmployees = User::whereHas('roles')->count();
                $activeEmployees = User::where('status', 'actif')->count();
                $newEmployees = User::where('created_at', '>=', now()->subDays(30))->count();
                $departmentDistribution = \App\Models\Department::withCount('users')->get();
                $largestDepartment = $departmentDistribution->sortByDesc('users_count')->first();
                $largestDepartmentName = $largestDepartment ? $largestDepartment->name : 'None';
                $largestDepartmentCount = $largestDepartment ? $largestDepartment->users_count : 0;

                $this->resumeTitle = 'Employees';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Employee Management',
                    'description' => 'Comprehensive management of all employee records in the organization. This page provides a complete listing of employees with filtering and search capabilities. Use this interface to view, create, edit, and manage employee information, including personal details, role assignments, department affiliations, and employment status.',
                    'metrics' => [
                        ['label' => 'Total Employees', 'value' => $totalEmployees, 'change' => null],
                        ['label' => 'Active Employees', 'value' => $activeEmployees, 'change' => $totalEmployees > 0 ? round(($activeEmployees / $totalEmployees) * 100) : 0],
                        ['label' => 'New Employees', 'value' => $newEmployees, 'change' => null, 'description' => 'Last 30 days'],
                        ['label' => 'Largest Department', 'value' => $largestDepartmentCount, 'description' => $largestDepartmentName]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Employees';
                $this->current_page_resume['description'] = 'Manage employee records';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'user-group';
                break;

            case 'hr.employees.create':
                $availableRoles = \App\Models\Role::count();
                $availableDepartments = \App\Models\Department::count();

                $this->resumeTitle = 'Create Employee';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Employee',
                    'description' => 'Add a new employee record to the system. This form allows you to create a comprehensive employee profile with all necessary information including personal details, contact information, role assignment, department affiliation, and employment terms.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide complete personal and contact information',
                        'Upload necessary identification and qualification documents',
                        'Assign appropriate role based on job responsibilities',
                        'Select the correct department for organizational placement',
                        'Set employment status and start date',
                        'Add emergency contact information if available',
                        'You can add contracts and additional documents after creating the employee record'
                    ],
                    'stats' => [
                        'Available Roles' => $availableRoles,
                        'Available Departments' => $availableDepartments,
                        'Current Employees' => User::whereHas('roles')->count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Employee';
                $this->current_page_resume['description'] = 'Add a new employee record';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'user-plus';
                break;

            case 'hr.employees.show':
                $employeeName = $this->employee ? $this->employee->getFullNameAttribute() : 'Employee';
                $roleName = $this->employee && $this->employee->role ? $this->employee->role->name : 'Not assigned';
                $departmentName = $this->employee && $this->employee->department ? $this->employee->department->name : 'Not assigned';
                $supervisorName = $this->employee && $this->employee->supervisor ? $this->employee->supervisor->getFullNameAttribute() : 'None';
                $subordinatesCount = $this->employee ? $this->employee->subordinates()->count() : 0;
                $documentsCount = $this->employee ? $this->employee->documents()->count() : 0;
                $contractsCount = $this->employee ? $this->employee->contracts()->count() : 0;

                $this->resumeTitle = $employeeName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $employeeName,
                    'subtitle' => 'Employee Details',
                    'description' => 'Detailed profile for ' . $employeeName . '. This page provides comprehensive information about the employee\'s personal details, role, department, reporting relationships, documents, and employment history. Use this information to manage the employee\'s record and access related HR functions.',
                    'stats' => [
                        'Role' => $roleName,
                        'Status' => $this->employee ? ucfirst($this->employee->status) : 'Unknown',
                        'Department' => $departmentName,
                        'Supervisor' => $supervisorName,
                        'Subordinates' => $subordinatesCount,
                        'Documents' => $documentsCount,
                        'Contracts' => $contractsCount,
                        'Joined' => $this->employee && $this->employee->created_at ? $this->employee->created_at->format('M d, Y') : 'Unknown',
                        'Email' => $this->employee ? $this->employee->email : 'Not provided',
                        'Phone' => $this->employee ? $this->employee->phone : 'Not provided',
                        'Last Updated' => $this->employee && $this->employee->updated_at ? $this->employee->updated_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->employee ? $this->employee->name : 'Employee Details';
                $this->current_page_resume['description'] = 'View employee information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'user';
                break;

            case 'hr.employees.edit':
                $employeeName = $this->employee ? $this->employee->getFullNameAttribute() : 'Employee';
                $roleName = $this->employee && $this->employee->role ? $this->employee->role->name : 'Not assigned';
                $departmentName = $this->employee && $this->employee->department ? $this->employee->department->name : 'Not assigned';
                $documentsCount = $this->employee ? $this->employee->documents()->count() : 0;

                $this->resumeTitle = 'Edit: ' . $employeeName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $employeeName,
                    'description' => 'Update information and settings for ' . $employeeName . '. This form allows you to modify all aspects of the employee\'s profile, including personal details, contact information, role assignment, department affiliation, and employment status.',
                    'instructions' => [
                        'Update only the fields that need to be changed',
                        'You can modify personal and contact information',
                        'Change role assignment to reflect new responsibilities',
                        'Update department affiliation if transferred',
                        'Modify employment status if needed (active/inactive/on leave)',
                        'Update reporting relationships if organizational structure has changed',
                        'You can manage documents and contracts from their respective sections'
                    ],
                    'stats' => [
                        'Current Role' => $roleName,
                        'Current Department' => $departmentName,
                        'Documents' => $documentsCount,
                        'Status' => $this->employee ? ucfirst($this->employee->status) : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->employee ? 'Edit ' . $this->employee->name : 'Edit Employee';
                $this->current_page_resume['description'] = 'Update employee information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'pencil';
                break;

            case 'hr.employees.delete':
                $employeeName = $this->employee ? $this->employee->getFullNameAttribute() : 'Employee';
                $roleName = $this->employee && $this->employee->role ? $this->employee->role->name : 'Not assigned';
                $departmentName = $this->employee && $this->employee->department ? $this->employee->department->name : 'Not assigned';
                $documentsCount = $this->employee ? $this->employee->documents()->count() : 0;
                $contractsCount = $this->employee ? $this->employee->contracts()->count() : 0;
                $subordinatesCount = $this->employee ? $this->employee->subordinates()->count() : 0;

                $this->resumeTitle = 'Delete: ' . $employeeName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $employeeName,
                    'description' => 'You are about to permanently remove ' . $employeeName . ' from the system. This action will delete the employee record and all associated data.',
                    'warning' => 'This action cannot be undone. All associated data including documents, contracts, and employment history will be permanently deleted. Consider deactivating the employee instead if you need to maintain records for compliance or historical purposes.',
                    'stats' => [
                        'Role' => $roleName,
                        'Department' => $departmentName,
                        'Documents' => $documentsCount,
                        'Contracts' => $contractsCount,
                        'Subordinates' => $subordinatesCount,
                        'Joined' => $this->employee && $this->employee->created_at ? $this->employee->created_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->employee ? 'Delete ' . $this->employee->name : 'Delete Employee';
                $this->current_page_resume['description'] = 'Remove employee record';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'trash';
                break;

            // Document Management
            case 'hr.documents':
                $totalDocuments = Document::count();

                $this->resumeTitle = 'Documents';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Documents',
                    'description' => 'Manage employee documents',
                    'metrics' => [
                        ['label' => 'Total Documents', 'value' => $totalDocuments],
                        ['label' => 'Recent Uploads', 'value' => Document::where('created_at', '>=', now()->subDays(30))->count(), 'description' => 'Last 30 days']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Documents';
                $this->current_page_resume['description'] = 'Manage employee documents';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'document-text';
                break;

            case 'hr.documents.create':
                $this->resumeTitle = 'Create Document';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Document',
                    'description' => 'Upload a new document',
                    'instructions' => [
                        'Select the document file to upload',
                        'Provide a descriptive name and category',
                        'Assign the document to an employee if applicable'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Document';
                $this->current_page_resume['description'] = 'Upload a new document';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'document-add';
                break;

            case 'hr.documents.show':
                $documentName = $this->document ? $this->document->name : 'Document';

                $this->resumeTitle = $documentName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $documentName,
                    'subtitle' => 'Document Details',
                    'stats' => [
                        'Type' => $this->document ? ucfirst($this->document->type) : 'Unknown',
                        'Size' => $this->document && $this->document->size ? $this->formatFileSize($this->document->size) : 'Unknown',
                        'Uploaded' => $this->document && $this->document->created_at ? $this->document->created_at->format('M d, Y') : 'Unknown',
                        'Owner' => $this->document && $this->document->user ? $this->document->user->name : 'Not assigned'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->document ? $this->document->name : 'Document Details';
                $this->current_page_resume['description'] = 'View document information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'document';
                break;

            case 'hr.documents.edit':
                $documentName = $this->document ? $this->document->name : 'Document';

                $this->resumeTitle = 'Edit: ' . $documentName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $documentName,
                    'description' => 'Update document information',
                    'instructions' => [
                        'Update the document details',
                        'You can modify the name, category, and owner'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->document ? 'Edit ' . $this->document->name : 'Edit Document';
                $this->current_page_resume['description'] = 'Update document information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'pencil';
                break;

            case 'hr.documents.delete':
                $documentName = $this->document ? $this->document->name : 'Document';

                $this->resumeTitle = 'Delete: ' . $documentName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $documentName,
                    'description' => 'Remove document',
                    'warning' => 'This action cannot be undone. The document file will be permanently deleted.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->document ? 'Delete ' . $this->document->name : 'Delete Document';
                $this->current_page_resume['description'] = 'Remove document';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'trash';
                break;

            // Contract Management
            case 'hr.contracts':
                $totalContracts = Contract::count();
                $activeContracts = Contract::where('status', 'active')->count();
                $expiringContracts = Contract::where('status', 'active')
                    ->where('end_date', '<=', now()->addMonths(1))
                    ->where('end_date', '>', now())
                    ->count();
                $recentlyCreated = Contract::where('created_at', '>=', now()->subDays(30))->count();

                $this->resumeTitle = 'Contracts';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Contract Management',
                    'description' => 'Comprehensive management of all employee contracts in the organization. This page provides a complete listing of contracts with filtering and search capabilities. Use this interface to monitor contract status, track expiration dates, and manage employment agreements across the organization.',
                    'metrics' => [
                        ['label' => 'Total Contracts', 'value' => $totalContracts, 'change' => null],
                        ['label' => 'Active Contracts', 'value' => $activeContracts, 'change' => $totalContracts > 0 ? round(($activeContracts / $totalContracts) * 100) : 0],
                        ['label' => 'Expiring Soon', 'value' => $expiringContracts, 'change' => null, 'description' => 'Next 30 days'],
                        ['label' => 'Recently Created', 'value' => $recentlyCreated, 'change' => null, 'description' => 'Last 30 days']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Contracts';
                $this->current_page_resume['description'] = 'Manage employee contracts';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'clipboard-list';
                break;

            case 'hr.contracts.create':
                $availableEmployees = User::whereHas('roles')->where('status', 'actif')->count();
                $contractTypes = ['Full-time', 'Part-time', 'Temporary', 'Internship', 'Consultant']; // Example types

                $this->resumeTitle = 'Create Contract';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Contract',
                    'description' => 'Add a new employment contract to the system. This form allows you to create a comprehensive contract record with all necessary information including terms, conditions, duration, compensation, and employee assignment.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide a descriptive title for the contract',
                        'Select the employee to whom this contract applies',
                        'Specify the contract type (full-time, part-time, etc.)',
                        'Set the start and end dates for the contract period',
                        'Define compensation terms and payment schedule',
                        'Include any special clauses or conditions',
                        'Upload the signed contract document if available',
                        'Set the contract status (draft/active/completed)'
                    ],
                    'stats' => [
                        'Available Employees' => $availableEmployees,
                        'Contract Types' => count($contractTypes),
                        'Active Contracts' => Contract::where('status', 'active')->count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Contract';
                $this->current_page_resume['description'] = 'Add a new contract';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'clipboard-check';
                break;

            case 'hr.contracts.show':
                $contractTitle = $this->contract ? $this->contract->title : 'Contract';

                $this->resumeTitle = $contractTitle;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $contractTitle,
                    'subtitle' => 'Contract Details',
                    'stats' => [
                        'Status' => $this->contract ? ucfirst($this->contract->status) : 'Unknown',
                        'Start Date' => $this->contract && $this->contract->start_date ? $this->contract->start_date->format('M d, Y') : 'Not set',
                        'End Date' => $this->contract && $this->contract->end_date ? $this->contract->end_date->format('M d, Y') : 'Not set',
                        'Employee' => $this->contract && $this->contract->employee ? $this->contract->employee->name : 'Not assigned'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->contract ? $this->contract->title : 'Contract Details';
                $this->current_page_resume['description'] = 'View contract information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'clipboard';
                break;

            case 'hr.contracts.edit':
                $contractTitle = $this->contract ? $this->contract->title : 'Contract';

                $this->resumeTitle = 'Edit: ' . $contractTitle;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $contractTitle,
                    'description' => 'Update contract information',
                    'instructions' => [
                        'Update the necessary fields',
                        'You can modify contract terms, dates, and status'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->contract ? 'Edit ' . $this->contract->title : 'Edit Contract';
                $this->current_page_resume['description'] = 'Update contract information';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'pencil';
                break;

            case 'hr.contracts.delete':
                $contractTitle = $this->contract ? $this->contract->title : 'Contract';

                $this->resumeTitle = 'Delete: ' . $contractTitle;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $contractTitle,
                    'description' => 'Remove contract',
                    'warning' => 'This action cannot be undone. All associated data will also be removed.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->contract ? 'Delete ' . $this->contract->title : 'Delete Contract';
                $this->current_page_resume['description'] = 'Remove contract';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'trash';
                break;

            // Performance Management
            case 'hr.performance':
                $totalEmployees = User::whereHas('roles')->count();
                $ratedEmployees = User::whereNotNull('rating')->count();
                $averageRating = User::whereNotNull('rating')->avg('rating') ?? 0;
                $topPerformers = User::where('rating', '>=', 4)->count();
                $improvementNeeded = User::where('rating', '<', 3)->whereNotNull('rating')->count();

                $this->resumeTitle = 'Performance';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Performance Management',
                    'description' => 'Comprehensive overview of employee performance metrics across the organization. This dashboard provides key insights into performance ratings, top performers, improvement areas, and evaluation trends. Use this information to identify high performers, address performance gaps, and make informed decisions about training and development needs.',
                    'metrics' => [
                        ['label' => 'Average Rating', 'value' => number_format($averageRating, 1), 'suffix' => '/5', 'change' => null],
                        ['label' => 'Top Performers', 'value' => $topPerformers, 'change' => $ratedEmployees > 0 ? round(($topPerformers / $ratedEmployees) * 100) : 0],
                        ['label' => 'Improvement Needed', 'value' => $improvementNeeded, 'change' => $ratedEmployees > 0 ? round(($improvementNeeded / $ratedEmployees) * 100) : 0],
                        ['label' => 'Evaluated', 'value' => $ratedEmployees, 'change' => $totalEmployees > 0 ? round(($ratedEmployees / $totalEmployees) * 100) : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Performance';
                $this->current_page_resume['description'] = 'Employee performance metrics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['icon'] = 'chart-bar';
                break;

            // Attendance Management
            case 'hr.attendance':
                $totalActiveEmployees = User::where('status', 'actif')->count();
                $presentToday = round($totalActiveEmployees * 0.9); // Example calculation
                $absentToday = $totalActiveEmployees - $presentToday;
                $lateToday = round($presentToday * 0.15); // Example calculation
                $attendanceRate = $totalActiveEmployees > 0 ? round(($presentToday / $totalActiveEmployees) * 100) : 0;

                $this->resumeTitle = 'Attendance';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Attendance Management',
                    'description' => 'Comprehensive tracking of employee attendance across the organization. This dashboard provides real-time metrics on present and absent employees, tardiness, and attendance trends. Use this information to monitor workforce availability, address attendance issues, and ensure adequate staffing levels.',
                    'metrics' => [
                        ['label' => 'Present Today', 'value' => $presentToday, 'change' => $totalActiveEmployees > 0 ? round(($presentToday / $totalActiveEmployees) * 100) : 0],
                        ['label' => 'Absent Today', 'value' => $absentToday, 'change' => $totalActiveEmployees > 0 ? round(($absentToday / $totalActiveEmployees) * 100) : 0],
                        ['label' => 'Late Today', 'value' => $lateToday, 'change' => $presentToday > 0 ? round(($lateToday / $presentToday) * 100) : 0],
                        ['label' => 'Attendance Rate', 'value' => $attendanceRate, 'suffix' => '%', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Attendance';
                $this->current_page_resume['description'] = 'Employee attendance records';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['icon'] = 'calendar';
                break;

            // Onboarding Management
            case 'hr.onboarding':
                $activeOnboarding = 5; // Example value
                $completedThisMonth = 3; // Example value
                $pendingStart = 2; // Example value
                $averageCompletionDays = 14; // Example value

                $this->resumeTitle = 'Onboarding';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Onboarding Management',
                    'description' => 'Comprehensive management of employee onboarding processes across the organization. This dashboard provides visibility into active onboarding activities, completion rates, and upcoming onboarding needs. Use this information to ensure smooth integration of new employees and monitor onboarding effectiveness.',
                    'metrics' => [
                        ['label' => 'Active Onboarding', 'value' => $activeOnboarding, 'change' => null],
                        ['label' => 'Completed This Month', 'value' => $completedThisMonth, 'change' => null],
                        ['label' => 'Pending Start', 'value' => $pendingStart, 'change' => null],
                        ['label' => 'Avg. Completion Time', 'value' => $averageCompletionDays, 'suffix' => ' days', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Onboarding';
                $this->current_page_resume['description'] = 'Employee onboarding processes';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'user-add';
                break;

            case 'hr.onboarding.create':
                $newEmployees = User::where('created_at', '>=', now()->subDays(30))
                    ->where('status', 'actif')
                    ->count();
                $availableTemplates = 4; // Example value

                $this->resumeTitle = 'Create Onboarding';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Onboarding Process',
                    'description' => 'Start a new employee onboarding process in the system. This form allows you to set up a structured onboarding experience with all necessary steps, resources, and timelines to ensure smooth integration of new employees into the organization.',
                    'instructions' => [
                        'Select the employee who will undergo onboarding',
                        'Choose an appropriate onboarding template based on role and department',
                        'Set start and expected completion dates for the onboarding process',
                        'Assign mentors or buddies if applicable',
                        'Customize onboarding steps if needed',
                        'Specify any special requirements or accommodations',
                        'Set up notifications for key onboarding milestones',
                        'You can track onboarding progress after creation'
                    ],
                    'stats' => [
                        'New Employees' => $newEmployees,
                        'Available Templates' => $availableTemplates,
                        'Active Onboarding' => 5 // Example value
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Onboarding';
                $this->current_page_resume['description'] = 'Start a new onboarding process';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'plus-circle';
                break;

            case 'hr.onboarding.show':
                $this->resumeTitle = 'Onboarding Details';
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Onboarding Details',
                    'subtitle' => 'Onboarding Process Information',
                    'stats' => [
                        'Employee' => $this->onboarding && $this->onboarding->employee ? $this->onboarding->employee->name : 'Not assigned',
                        'Status' => $this->onboarding ? ucfirst($this->onboarding->status) : 'Unknown',
                        'Start Date' => $this->onboarding && $this->onboarding->start_date ? $this->onboarding->start_date->format('M d, Y') : 'Not set',
                        'Progress' => $this->onboarding ? $this->onboarding->progress . '%' : '0%'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Onboarding Details';
                $this->current_page_resume['description'] = 'View onboarding process details';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'clipboard-check';
                break;

            case 'hr.onboarding.templates':
                $this->resumeTitle = 'Onboarding Templates';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Onboarding Templates',
                    'description' => 'Manage onboarding templates',
                    'metrics' => [
                        ['label' => 'Total Templates', 'value' => '5'],
                        ['label' => 'Active Templates', 'value' => '3']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Onboarding Templates';
                $this->current_page_resume['description'] = 'Manage onboarding templates';
                $this->current_page_resume['type'] = 'infos';
                $this->current_page_resume['icon'] = 'template';
                break;

            default:
                $this->resumeTitle = 'Human Resources';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Human Resources',
                    'description' => 'Comprehensive human resources management system for employee administration, document management, contract tracking, performance evaluation, and onboarding processes. This module allows you to manage all aspects of employee lifecycle from hiring to retirement. Use the navigation menu to access different sections of the HR management system.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Human Resources';
                $this->current_page_resume['description'] = 'HR Management System';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['icon'] = 'users';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'hr-dashboard-module',
                'title' => 'Dashboard',
                'description' => 'HR Dashboard',
                'route' => 'hr.index',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['show_hr_dashboard'],
                'icon' => 'home',
                'sections' => []
            ],
            [
                'module_id' => 'hr-employees-module',
                'title' => 'Employees',
                'description' => 'Employee Management',
                'route' => 'hr.employees',
                'section_routes' => ['hr.employees.create', 'hr.employees.edit', 'hr.employees.delete', 'hr.employees.show'],
                'display' => true,
                'authorized_permissions' => ['manage_employees'],
                'icon' => 'user-group',
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create an employee record',
                        'route' => 'hr.employees.create',
                        'display' => true,
                        'authorized_permissions' => ['create_employees'],
                        'icon' => 'user-plus',
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit an employee record',
                        'route' => 'hr.employees.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_employees'],
                        'icon' => 'pencil',
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete an employee record',
                        'route' => 'hr.employees.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_employees'],
                        'icon' => 'trash',
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show an employee record',
                        'route' => 'hr.employees.show',
                        'display' => true,
                        'authorized_permissions' => ['show_employees'],
                        'icon' => 'eye',
                    ]
                ]
            ],
            [
                'module_id' => 'hr-documents-module',
                'title' => 'Documents',
                'description' => 'Document Management',
                'route' => 'hr.documents',
                'section_routes' => ['hr.documents.create', 'hr.documents.edit', 'hr.documents.delete', 'hr.documents.show'],
                'display' => true,
                'authorized_permissions' => ['manage_hr_documents'],
                'icon' => 'document-text',
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a document',
                        'route' => 'hr.documents.create',
                        'display' => true,
                        'authorized_permissions' => ['create_hr_documents'],
                        'icon' => 'document-add',
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a document',
                        'route' => 'hr.documents.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_hr_documents'],
                        'icon' => 'pencil',
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a document',
                        'route' => 'hr.documents.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_hr_documents'],
                        'icon' => 'trash',
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a document',
                        'route' => 'hr.documents.show',
                        'display' => true,
                        'authorized_permissions' => ['show_hr_documents'],
                        'icon' => 'eye',
                    ]
                ]
            ],
            [
                'module_id' => 'hr-contracts-module',
                'title' => 'Contracts',
                'description' => 'Contract Management',
                'route' => 'hr.contracts',
                'section_routes' => ['hr.contracts.create', 'hr.contracts.edit', 'hr.contracts.delete', 'hr.contracts.show'],
                'display' => true,
                'authorized_permissions' => ['manage_hr_contracts'],
                'icon' => 'clipboard-list',
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a contract',
                        'route' => 'hr.contracts.create',
                        'display' => true,
                        'authorized_permissions' => ['create_hr_contracts'],
                        'icon' => 'clipboard-check',
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a contract',
                        'route' => 'hr.contracts.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_hr_contracts'],
                        'icon' => 'pencil',
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a contract',
                        'route' => 'hr.contracts.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_hr_contracts'],
                        'icon' => 'trash',
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a contract',
                        'route' => 'hr.contracts.show',
                        'display' => true,
                        'authorized_permissions' => ['show_hr_contracts'],
                        'icon' => 'eye',
                    ]
                ]
            ],
            [
                'module_id' => 'hr-performance-module',
                'title' => 'Performance',
                'description' => 'Performance Management',
                'route' => 'hr.performance',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['manage_hr'],
                'icon' => 'chart-bar',
                'sections' => []
            ],
            [
                'module_id' => 'hr-attendance-module',
                'title' => 'Attendance',
                'description' => 'Attendance Management',
                'route' => 'hr.attendance',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['manage_hr_attendance'],
                'icon' => 'calendar',
                'sections' => []
            ],
            [
                'module_id' => 'hr-onboarding-module',
                'title' => 'Onboarding',
                'description' => 'Employee Onboarding',
                'route' => 'hr.onboarding',
                'section_routes' => ['hr.onboarding.create', 'hr.onboarding.show', 'hr.onboarding.templates'],
                'display' => true,
                'authorized_permissions' => ['manage_hr_onboarding'],
                'icon' => 'user-add',
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Start new onboarding process',
                        'route' => 'hr.onboarding.create',
                        'display' => true,
                        'authorized_permissions' => ['create_hr_onboarding'],
                        'icon' => 'plus-circle',
                    ],
                    [
                        'title' => 'Templates',
                        'description' => 'Manage onboarding templates',
                        'route' => 'hr.onboarding.templates',
                        'display' => true,
                        'authorized_permissions' => ['manage_hr_onboarding_templates'],
                        'icon' => 'template',
                    ]
                ]
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    /**
     * Format file size in human-readable format
     *
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } elseif ($bytes > 1) {
            return $bytes . ' bytes';
        } elseif ($bytes == 1) {
            return $bytes . ' byte';
        } else {
            return '0 bytes';
        }
    }

    public function render()
    {
        return view('livewire.hr.hr-page', [
            'employee' => $this->employee,
            'document' => $this->document,
            'contract' => $this->contract,
            'onboarding' => $this->onboarding,
            'onboardingTemplate' => $this->onboardingTemplate,
            'pages' => $this->pages,
            'current_page' => $this->current_page,
            'current_page_resume' => $this->current_page_resume,
            'current_page_section' => $this->current_page_section,
        ]);
    }
}
