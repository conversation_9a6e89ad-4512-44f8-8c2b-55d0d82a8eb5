<?php

namespace App\Livewire\Accountant;

use App\Livewire\Global\Page;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Livewire\Attributes\On;

class AccountantPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-accountant-invoices-create')]
    public function toAccountantInvoicesCreate()
    {
        return $this->redirect(route('accountant.invoices.create'), navigate: true);
    }

    #[On('to-accountant-invoices')]
    public function toAccountantInvoices()
    {
        return $this->redirect(route('accountant.invoices'), navigate: true);
    }

    #[On('to-accountant-invoices-show')]
    public function toAccountantInvoicesShow($params)
    {
        return $this->redirect(route('accountant.invoices.show', ['invoice' => $params['invoice']]), navigate: true);
    }

    #[On('to-accountant-invoices-edit')]
    public function toAccountantInvoicesEdit($params)
    {
        return $this->redirect(route('accountant.invoices.edit', ['invoice' => $params['invoice']]), navigate: true);
    }

    #[On('to-accountant-invoices-delete')]
    public function toAccountantInvoicesDelete($params)
    {
        return $this->redirect(route('accountant.invoices.delete', ['invoice' => $params['invoice']]), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Accountant';
        $this->resumeDescription = 'Financial management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Accountant',
            'description' => 'Comprehensive financial management system for tracking payments, calculating salaries, generating invoices, and producing financial reports. This module allows you to manage all financial aspects of the call center operations.'
        ];

        switch ($routeName) {
            case 'accountant.agent.attendance':
                $activeAgents = User::where('status', 'actif')->count();
                $totalAgents = User::whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })->count();
                $attendanceRate = $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0;
                $inactiveAgents = $totalAgents - $activeAgents;
                $absenteeismRate = $totalAgents > 0 ? round(($inactiveAgents / $totalAgents) * 100) : 0;

                $this->resumeTitle = 'Attendance';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agent Attendance Tracking',
                    'description' => 'Comprehensive tracking of agent attendance for payroll and performance evaluation. This dashboard provides real-time metrics on agent presence, absence rates, and attendance trends. Use this information to monitor workforce availability, calculate attendance-based compensation, and identify attendance patterns that may affect financial outcomes.',
                    'metrics' => [
                        ['label' => 'Active Agents', 'value' => $activeAgents, 'change' => $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0],
                        ['label' => 'Total Agents', 'value' => $totalAgents, 'change' => null],
                        ['label' => 'Inactive Agents', 'value' => $inactiveAgents, 'change' => null],
                        ['label' => 'Attendance Rate', 'value' => $attendanceRate, 'suffix' => '%', 'change' => null],
                        ['label' => 'Absenteeism Rate', 'value' => $absenteeismRate, 'suffix' => '%', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Attendance';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $activeAgents;
                $this->current_page_resume['description'] = 'Active Agents';
                break;

            case 'accountant.payment.management':
                $totalPayments = Payment::count();
                $pendingPayments = Payment::where('status', 'pending')->count();
                $completedPayments = Payment::where('status', 'paid')->count();
                $totalAmount = Payment::sum('amount');
                $avgPaymentAmount = Payment::avg('amount');
                $pendingAmount = Payment::where('status', 'pending')->sum('amount');

                $this->resumeTitle = 'Payment Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Payment Management',
                    'description' => 'Comprehensive management of all agent payments in the system. This dashboard provides a complete overview of payment status, amounts, and processing metrics. Use this interface to track pending payments, monitor disbursement progress, and ensure timely compensation for all agents.',
                    'metrics' => [
                        ['label' => 'Total Payments', 'value' => $totalPayments, 'change' => null],
                        ['label' => 'Pending Payments', 'value' => $pendingPayments, 'change' => $totalPayments > 0 ? round(($pendingPayments / $totalPayments) * 100) : 0],
                        ['label' => 'Completed Payments', 'value' => $completedPayments, 'change' => $totalPayments > 0 ? round(($completedPayments / $totalPayments) * 100) : 0],
                        ['label' => 'Total Amount', 'value' => number_format($totalAmount, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Pending Amount', 'value' => number_format($pendingAmount, 2), 'prefix' => '$', 'change' => $totalAmount > 0 ? round(($pendingAmount / $totalAmount) * 100) : 0],
                        ['label' => 'Avg Payment', 'value' => number_format($avgPaymentAmount, 2), 'prefix' => '$', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Payment';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalPayments;
                $this->current_page_resume['description'] = 'Total Payments';
                break;

            case 'accountant.salary.calculation':
                $totalPaid = Payment::sum('amount');
                $avgSalary = Payment::avg('amount');
                $paymentCount = Payment::count();
                $highestSalary = Payment::max('amount');
                $lowestSalary = Payment::min('amount');
                $salaryRange = $highestSalary - $lowestSalary;

                // Get current month's total
                $currentMonth = date('m');
                $currentYear = date('Y');

                // Use database-agnostic approach for date functions
                $connection = config('database.default');
                $driver = config("database.connections.{$connection}.driver");

                // Get current month's total
                if ($driver === 'sqlite') {
                    $currentMonthTotal = Payment::whereRaw("strftime('%m', created_at) = ? AND strftime('%Y', created_at) = ?", [$currentMonth, $currentYear])->sum('amount');
                } else {
                    $currentMonthTotal = Payment::whereMonth('created_at', $currentMonth)->whereYear('created_at', $currentYear)->sum('amount');
                }

                $this->resumeTitle = 'Salary Calculation';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Salary Calculation',
                    'description' => 'Comprehensive salary calculation system for determining agent compensation. This dashboard provides detailed metrics on salary distributions, payment processing, and compensation trends. Use this interface to calculate salaries based on performance, attendance, and other factors, ensuring fair and accurate compensation for all agents.',
                    'metrics' => [
                        ['label' => 'Total Paid', 'value' => number_format($totalPaid, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Average Salary', 'value' => number_format($avgSalary, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Payment Count', 'value' => $paymentCount, 'change' => null],
                        ['label' => 'Current Month', 'value' => number_format($currentMonthTotal, 2), 'prefix' => '$', 'change' => $totalPaid > 0 ? round(($currentMonthTotal / $totalPaid) * 100) : 0],
                        ['label' => 'Highest Salary', 'value' => number_format($highestSalary, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Salary Range', 'value' => number_format($salaryRange, 2), 'prefix' => '$', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Salary';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalPaid;
                $this->current_page_resume['description'] = 'Total Paid';
                break;

            case 'accountant.payment.history':
                $completedPayments = Payment::where('status', 'paid')->count();
                $totalPayments = Payment::count();
                $completionRate = $totalPayments > 0 ? round(($completedPayments / $totalPayments) * 100) : 0;
                $totalPaidAmount = Payment::where('status', 'paid')->sum('amount');

                // Get recent payments (last 30 days)
                $connection = config('database.default');
                $driver = config("database.connections.{$connection}.driver");

                if ($driver === 'sqlite') {
                    $recentPayments = Payment::whereRaw("julianday('now') - julianday(created_at) <= 30")->count();
                    $recentPaidAmount = Payment::where('status', 'paid')->whereRaw("julianday('now') - julianday(created_at) <= 30")->sum('amount');
                } else {
                    $recentPayments = Payment::where('created_at', '>=', now()->subDays(30))->count();
                    $recentPaidAmount = Payment::where('status', 'paid')->where('created_at', '>=', now()->subDays(30))->sum('amount');
                }

                $this->resumeTitle = 'Payment History';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Payment History',
                    'description' => 'Comprehensive historical record of all payments processed through the system. This dashboard provides detailed metrics on payment completion rates, historical trends, and disbursement patterns. Use this information to analyze payment history, track financial activities over time, and generate reports for accounting and compliance purposes.',
                    'metrics' => [
                        ['label' => 'Completed Payments', 'value' => $completedPayments, 'change' => $totalPayments > 0 ? round(($completedPayments / $totalPayments) * 100) : 0],
                        ['label' => 'Total Payments', 'value' => $totalPayments, 'change' => null],
                        ['label' => 'Completion Rate', 'value' => $completionRate, 'suffix' => '%', 'change' => null],
                        ['label' => 'Total Paid Amount', 'value' => number_format($totalPaidAmount, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Recent Payments', 'value' => $recentPayments, 'change' => $totalPayments > 0 ? round(($recentPayments / $totalPayments) * 100) : 0, 'description' => 'Last 30 days'],
                        ['label' => 'Recent Paid Amount', 'value' => number_format($recentPaidAmount, 2), 'prefix' => '$', 'change' => $totalPaidAmount > 0 ? round(($recentPaidAmount / $totalPaidAmount) * 100) : 0, 'description' => 'Last 30 days']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Payment History';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $completedPayments;
                $this->current_page_resume['description'] = 'Completed Payments';
                break;

            case 'accountant.reports':
                $currentYear = date('Y');
                $previousYear = $currentYear - 1;

                // Use database-agnostic approach for date functions
                $connection = config('database.default');
                $driver = config("database.connections.{$connection}.driver");

                // Get total revenue for the current year
                if ($driver === 'sqlite') {
                    $totalRevenue = Payment::whereRaw("strftime('%Y', created_at) = ?", [$currentYear])->sum('amount');
                    $previousYearRevenue = Payment::whereRaw("strftime('%Y', created_at) = ?", [$previousYear])->sum('amount');
                } else {
                    $totalRevenue = Payment::whereYear('created_at', $currentYear)->sum('amount');
                    $previousYearRevenue = Payment::whereYear('created_at', $previousYear)->sum('amount');
                }

                // Calculate year-over-year change
                $yoyChange = $previousYearRevenue > 0 ? round((($totalRevenue - $previousYearRevenue) / $previousYearRevenue) * 100) : null;

                // Get monthly average
                if ($driver === 'sqlite') {
                    $monthlyAvg = Payment::whereRaw("strftime('%Y', created_at) = ?", [$currentYear])
                        ->selectRaw("strftime('%m', created_at) as month, SUM(amount) as total")
                        ->groupBy('month')
                        ->get()
                        ->avg('total');

                    // Get current quarter total
                    $currentQuarter = ceil(date('m') / 3);
                    $quarterStart = (($currentQuarter - 1) * 3) + 1;
                    $quarterEnd = $quarterStart + 2;

                    $quarterTotal = Payment::whereRaw("strftime('%Y', created_at) = ?", [$currentYear])
                        ->whereRaw("CAST(strftime('%m', created_at) AS INTEGER) BETWEEN ? AND ?", [$quarterStart, $quarterEnd])
                        ->sum('amount');
                } else {
                    $monthlyAvg = Payment::whereYear('created_at', $currentYear)
                        ->selectRaw('MONTH(created_at) as month, SUM(amount) as total')
                        ->groupBy('month')
                        ->get()
                        ->avg('total');

                    // Get current quarter total
                    $currentQuarter = ceil(date('m') / 3);
                    $quarterStart = (($currentQuarter - 1) * 3) + 1;
                    $quarterEnd = $quarterStart + 2;

                    $quarterTotal = Payment::whereYear('created_at', $currentYear)
                        ->whereRaw('MONTH(created_at) BETWEEN ? AND ?', [$quarterStart, $quarterEnd])
                        ->sum('amount');
                }

                $this->resumeTitle = 'Financial Reports';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Financial Reports',
                    'description' => 'Comprehensive financial reporting system for analyzing revenue, expenses, and financial performance. This dashboard provides detailed metrics on financial trends, period comparisons, and key financial indicators. Use this information to generate financial reports, analyze business performance, and make data-driven financial decisions.',
                    'metrics' => [
                        ['label' => 'Total Revenue', 'value' => number_format($totalRevenue, 2), 'prefix' => '$', 'change' => $yoyChange],
                        ['label' => 'Monthly Average', 'value' => number_format($monthlyAvg, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Current Quarter', 'value' => number_format($quarterTotal, 2), 'prefix' => '$', 'change' => $totalRevenue > 0 ? round(($quarterTotal / $totalRevenue) * 100) : 0],
                        ['label' => 'Previous Year', 'value' => number_format($previousYearRevenue, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Current Year', 'value' => $currentYear, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Financial Reports';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $currentYear;
                $this->current_page_resume['description'] = 'Current Year';
                break;

            case 'accountant.invoices':
            case 'accountant.invoices.create':
            case 'accountant.invoices.show':
            case 'accountant.invoices.edit':
            case 'accountant.invoices.delete':
                $totalInvoices = 0;
                $paidInvoices = 0;
                $pendingInvoices = 0;
                $totalAmount = 0;
                $paidAmount = 0;
                $overdueInvoices = 0;

                if (Schema::hasTable('invoices')) {
                    $totalInvoices = Invoice::count();
                    $paidInvoices = Invoice::where('status', 'paid')->count();
                    $pendingInvoices = Invoice::where('status', 'pending')->count();
                    $totalAmount = Invoice::sum('amount');
                    $paidAmount = Invoice::where('status', 'paid')->sum('amount');

                    // Use database-agnostic approach for date functions
                    $connection = config('database.default');
                    $driver = config("database.connections.{$connection}.driver");

                    if ($driver === 'sqlite') {
                        $overdueInvoices = Invoice::where('status', 'pending')
                            ->whereRaw("julianday('now') > julianday(due_date)")
                            ->count();
                    } else {
                        $overdueInvoices = Invoice::where('status', 'pending')
                            ->where('due_date', '<', now())
                            ->count();
                    }
                }

                $this->resumeTitle = 'Invoices';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Invoice Management',
                    'description' => 'Comprehensive invoice management system for creating, tracking, and processing customer invoices. This dashboard provides detailed metrics on invoice status, payment collection, and outstanding balances. Use this interface to generate new invoices, monitor payment status, and manage the complete invoicing lifecycle.',
                    'metrics' => [
                        ['label' => 'Total Invoices', 'value' => $totalInvoices, 'change' => null],
                        ['label' => 'Paid Invoices', 'value' => $paidInvoices, 'change' => $totalInvoices > 0 ? round(($paidInvoices / $totalInvoices) * 100) : 0],
                        ['label' => 'Pending Invoices', 'value' => $pendingInvoices, 'change' => $totalInvoices > 0 ? round(($pendingInvoices / $totalInvoices) * 100) : 0],
                        ['label' => 'Overdue Invoices', 'value' => $overdueInvoices, 'change' => $pendingInvoices > 0 ? round(($overdueInvoices / $pendingInvoices) * 100) : 0],
                        ['label' => 'Total Amount', 'value' => number_format($totalAmount, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Collected Amount', 'value' => number_format($paidAmount, 2), 'prefix' => '$', 'change' => $totalAmount > 0 ? round(($paidAmount / $totalAmount) * 100) : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Invoices';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalInvoices;
                $this->current_page_resume['description'] = 'Total Invoices';
                break;

            default:
                $totalPayments = Payment::sum('amount') ?? 0;
                $pendingPayments = Payment::where('status', 'pending')->count();
                $completedPayments = Payment::where('status', 'paid')->count();
                $totalInvoices = 0;
                $overdueInvoices = 0;

                if (Schema::hasTable('invoices')) {
                    $totalInvoices = Invoice::count();

                    // Use database-agnostic approach for date functions
                    $connection = config('database.default');
                    $driver = config("database.connections.{$connection}.driver");

                    if ($driver === 'sqlite') {
                        $overdueInvoices = Invoice::where('status', 'pending')
                            ->whereRaw("julianday('now') > julianday(due_date)")
                            ->count();
                    } else {
                        $overdueInvoices = Invoice::where('status', 'pending')
                            ->where('due_date', '<', now())
                            ->count();
                    }
                }

                // Get current month's total
                $currentMonth = date('m');
                $currentYear = date('Y');

                if ($driver === 'sqlite') {
                    $currentMonthTotal = Payment::whereRaw("strftime('%m', created_at) = ? AND strftime('%Y', created_at) = ?", [$currentMonth, $currentYear])->sum('amount');
                } else {
                    $currentMonthTotal = Payment::whereMonth('created_at', $currentMonth)->whereYear('created_at', $currentYear)->sum('amount');
                }

                $this->resumeTitle = 'Accountant Dashboard';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Financial Management Dashboard',
                    'description' => 'Comprehensive financial management dashboard providing a complete overview of the organization\'s financial status. This dashboard presents key financial metrics, payment processing status, invoicing information, and revenue trends. Use this information to monitor financial health, track payment activities, and make informed financial decisions.',
                    'metrics' => [
                        ['label' => 'Total Payments', 'value' => number_format($totalPayments, 2), 'prefix' => '$', 'change' => null],
                        ['label' => 'Current Month', 'value' => number_format($currentMonthTotal, 2), 'prefix' => '$', 'change' => $totalPayments > 0 ? round(($currentMonthTotal / $totalPayments) * 100) : 0],
                        ['label' => 'Pending Payments', 'value' => $pendingPayments, 'change' => null],
                        ['label' => 'Completed Payments', 'value' => $completedPayments, 'change' => null],
                        ['label' => 'Total Invoices', 'value' => $totalInvoices, 'change' => null],
                        ['label' => 'Overdue Invoices', 'value' => $overdueInvoices, 'change' => $totalInvoices > 0 ? round(($overdueInvoices / $totalInvoices) * 100) : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Accountant Dashboard';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalPayments;
                $this->current_page_resume['description'] = 'Total Payments';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'title' => 'Dashboard',
                'description' => 'Accountant Dashboard',
                'route' => 'accountant.index',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Attendance',
                'description' => 'Agent attendance tracking',
                'route' => 'accountant.agent.attendance',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Payment Management',
                'description' => 'Manage agent payments',
                'route' => 'accountant.payment.management',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Payment History',
                'description' => 'View payment history',
                'route' => 'accountant.payment.history',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Salary Calculation',
                'description' => 'Calculate agent salaries',
                'route' => 'accountant.salary.calculation',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Financial Reports',
                'description' => 'Generate financial reports',
                'route' => 'accountant.reports',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Invoices',
                'description' => 'Manage invoices',
                'route' => 'accountant.invoices',
                'display' => true,
                'authorized_roles' => [1, 2, 11],
                'section_routes' => [
                    'accountant.invoices.create',
                    'accountant.invoices.show',
                    'accountant.invoices.edit',
                    'accountant.invoices.delete'
                ],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        return view('livewire.accountant.accountant-page');
    }
}
