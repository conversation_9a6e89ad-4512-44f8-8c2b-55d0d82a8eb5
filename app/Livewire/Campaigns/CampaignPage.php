<?php

namespace App\Livewire\Campaigns;

use App\Livewire\Global\Page;
use App\Models\Campaign;
use App\Models\Customer;
use App\Models\User;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\DB;


class CampaignPage extends Page
{
    public ?Campaign $campaign = null;
    public ?User $agent = null;
    public ?Customer $customer = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-campaign-index')]
    public function toCampaignIndex()
    {
        return $this->redirect(route('campaigns.index'), navigate: true);
    }

    #[On('to-campaign-show')]
    public function toCampaignShow(Campaign $campaign)
    {
        return $this->redirect(route('campaigns.show', ['campaign' => $campaign]), navigate: true);
    }

    #[On('to-campaign-edit')]
    public function toCampaignEdit(Campaign $campaign)
    {
        return $this->redirect(route('campaigns.edit', ['campaign' => $campaign]), navigate: true);
    }

    #[On('to-campaign-delete')]
    public function toCampaignDelete(Campaign $campaign)
    {
        return $this->redirect(route('campaigns.delete', ['campaign' => $campaign]), navigate: true);
    }

    #[On('to-campaign-create')]
    public function toCampaignCreate()
    {
        return $this->redirect(route('campaigns.create'), navigate: true);
    }

    #[On('to-campaign-agent')]
    public function toCampaignAgent()
    {
        return $this->redirect(route('campaigns.agents'), navigate: true);
    }

    #[On('to-campaign-agent-observation')]
    public function toCampaignAgentObservation(Agent $agent)
    {
        return $this->redirect(route('campaigns.agent.observation', ['agent' => $agent]), navigate: true);
    }

    #[On('to-campaign-agent-report')]
    public function toCampaignAgentReport(Agent $agent)
    {
        return $this->redirect(route('campaigns.agent.report', ['agent' => $agent]), navigate: true);
    }

    #[On('to-agent-create')]
    public function toAgentCreate()
    {
        return $this->redirect(route('agents.create'), navigate: true);
    }

    #[On('addToCampaign')]
    public function addToCampaign()
    {
        return $this->redirect(route('campaigns.add-agents'), navigate: true);
    }

    // Customer navigation methods
    #[On('to-customer-index')]
    public function toCustomerIndex()
    {
        return $this->redirect(route('campaigns.customers.index'), navigate: true);
    }

    #[On('to-customer-create')]
    public function toCustomerCreate()
    {
        return $this->redirect(route('campaigns.customers.create'), navigate: true);
    }

    #[On('to-customer-edit')]
    public function toCustomerEdit(Customer $customer)
    {
        return $this->redirect(route('campaigns.customers.edit', ['customer' => $customer]), navigate: true);
    }

    #[On('to-customer-show')]
    public function toCustomerShow(Customer $customer)
    {
        return $this->redirect(route('campaigns.customers.show', ['customer' => $customer]), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Campaigns';
        $this->resumeDescription = 'Campaign management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Campaigns',
            'description' => 'Comprehensive campaign management system for organizing, tracking, and optimizing call center marketing campaigns. This module allows you to create and manage campaigns, assign agents, track performance, and generate reports.'
        ];

        switch ($routeName) {
            case 'campaigns.index':
                $totalCampaigns = Campaign::count();
                $activeCampaigns = Campaign::where('status', 'active')->count();
                $inactiveCampaigns = $totalCampaigns - $activeCampaigns;
                $totalCustomers = Customer::count();

                $this->resumeTitle = 'All Campaigns';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Campaign Management',
                    'description' => 'Comprehensive overview of all marketing campaigns in the system. This page allows you to monitor campaign status, track performance metrics, and manage campaign records. Use the filters and search functionality to find specific campaigns based on status, customer, or other criteria.',
                    'metrics' => [
                        ['label' => 'Total Campaigns', 'value' => $totalCampaigns, 'change' => null],
                        ['label' => 'Active Campaigns', 'value' => $activeCampaigns, 'change' => $totalCampaigns > 0 ? round(($activeCampaigns / $totalCampaigns) * 100) : 0],
                        ['label' => 'Inactive Campaigns', 'value' => $inactiveCampaigns, 'change' => null],
                        ['label' => 'Customers', 'value' => $totalCustomers, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All campaigns';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'campaigns.create':
                $this->resumeTitle = 'New Campaign';
                $this->resumeDescription = 'Create a new campaign in the system';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'New Campaign',
                    'description' => 'Create a new marketing campaign in the system. This form allows you to set up a campaign with all necessary information including name, description, objectives, target metrics, customer assignment, and operational parameters.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide a descriptive name and clear objectives for the campaign',
                        'Set the campaign status (active/inactive)',
                        'Assign a customer to the campaign',
                        'Define target metrics and success criteria',
                        'Specify the campaign duration and schedule if applicable',
                        'You can assign agents to the campaign after creation'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'New campaign';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.show':
                if ($this->campaign) {
                    // Get additional campaign information
                    $agentCount = $this->campaign->agents->count();
                    $startDate = $this->campaign->start_date ? $this->campaign->start_date->format('M d, Y') : 'Not set';
                    $endDate = $this->campaign->end_date ? $this->campaign->end_date->format('M d, Y') : 'Not set';
                    $appointmentsCount = $this->campaign->appointments ? $this->campaign->appointments->count() : 0;

                    $this->resumeTitle = $this->campaign->name;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $this->campaign->name,
                        'subtitle' => 'Campaign Details',
                        'description' => 'Detailed information about the ' . $this->campaign->name . ' campaign. This page provides comprehensive details about the campaign\'s configuration, performance metrics, assigned agents, and customer information. Use this information to monitor and manage all aspects of the campaign.',
                        'stats' => [
                            'Status' => ucfirst($this->campaign->status ?? 'Unknown'),
                            'Customer' => $this->campaign->customer ? $this->campaign->customer->name : 'None',
                            'Agents' => $agentCount,
                            'Start Date' => $startDate,
                            'End Date' => $endDate,
                            'Appointments' => $appointmentsCount,
                            'Created' => $this->campaign->created_at ? $this->campaign->created_at->format('M d, Y') : 'Unknown',
                            'Last Updated' => $this->campaign->updated_at ? $this->campaign->updated_at->format('M d, Y') : 'Unknown'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->campaign->name;
                    $this->current_page_resume['type'] = 'chart';
                }
                break;

            case 'campaigns.edit':
                if ($this->campaign) {
                    $this->resumeTitle = 'Edit: ' . $this->campaign->name;
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit: ' . $this->campaign->name,
                        'description' => 'Update information and settings for the ' . $this->campaign->name . ' campaign. This form allows you to modify all aspects of the campaign configuration, including name, description, objectives, customer assignment, and operational parameters.',
                        'instructions' => [
                            'Update only the fields that need to be changed',
                            'You can modify the campaign name, description, and objectives',
                            'Changing the campaign status will affect its visibility and functionality',
                            'You can reassign the campaign to a different customer if needed',
                            'Update target metrics and success criteria if applicable',
                            'Modify campaign duration and schedule if necessary',
                            'Agent assignments can be managed from the Agents section'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->campaign->name;
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            case 'campaigns.delete':
                if ($this->campaign) {
                    $this->resumeTitle = 'Delete: ' . $this->campaign->name;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => 'Delete: ' . $this->campaign->name,
                        'description' => 'You are about to permanently remove the ' . $this->campaign->name . ' campaign from the system. This action will delete all campaign data and cannot be reversed.',
                        'warning' => 'This action cannot be undone. All campaign data, including agent assignments and performance records, will be permanently deleted. Consider deactivating the campaign instead if you may need to access this information in the future.',
                        'stats' => [
                            'Status' => ucfirst($this->campaign->status ?? 'Unknown'),
                            'Customer' => $this->campaign->customer ? $this->campaign->customer->name : 'None',
                            'Agents' => $this->campaign->agents->count(),
                            'Created' => $this->campaign->created_at ? $this->campaign->created_at->format('M d, Y') : 'Unknown'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->campaign->name;
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            case 'campaigns.agents':
                $totalAgents = User::whereIn('role_id', [5, 6])->count();
                $assignedAgents = $this->campaign ? $this->campaign->agents->count() : 0;
                $availableAgents = User::where('role_id', 6)->whereDoesntHave('campaigns', function($query) {
                    if ($this->campaign) {
                        $query->where('campaign_id', $this->campaign->id);
                    }
                })->count();

                $this->resumeTitle = 'Campaign Agents';
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Campaign Agents',
                    'description' => 'Manage agents assigned to this campaign. This page allows you to view, add, and remove agents from the campaign, as well as monitor their performance and activity. You can also observe agents and generate performance reports.',
                    'stats' => [
                        'Total Agents' => $totalAgents,
                        'Assigned Agents' => $assignedAgents,
                        'Available Agents' => $availableAgents,
                        'Campaign Completion' => $this->campaign && $this->campaign->target_appointments > 0 ?
                            round(($this->campaign->appointments()->count() / $this->campaign->target_appointments) * 100) . '%' : 'N/A'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Agents';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.add-agents':
                $availableAgents = User::where('role_id', 6)->whereDoesntHave('campaigns', function($query) {
                    if ($this->campaign) {
                        $query->where('campaign_id', $this->campaign->id);
                    }
                })->count();

                $this->resumeTitle = 'Add Agents from Training';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Add Agents from Training',
                    'description' => 'Add validated agents from training to this campaign. This page allows you to select agents who have completed training and assign them to the current campaign. Only agents who have been validated through the training process are eligible for assignment.',
                    'instructions' => [
                        'Select agents from the list of validated agents',
                        'Only agents who have completed and passed training can be added',
                        'You can select multiple agents at once for assignment',
                        'Agents already assigned to this campaign will not appear in the list',
                        'Consider agent skills and performance when making assignments',
                        'Newly assigned agents will be available immediately for campaign activities'
                    ],
                    'stats' => [
                        'Available Agents' => $availableAgents,
                        'Current Campaign Agents' => $this->campaign ? $this->campaign->agents->count() : 0
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Add Agents from Training';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.agent.observation':
                $recentObservations = $this->campaign ?
                    \App\Models\Observation::whereHas('agent', function($query) {
                        $query->whereHas('campaigns', function($q) {
                            $q->where('campaign_id', $this->campaign->id);
                        });
                    })->count() : 0;

                $this->resumeTitle = 'Recent Observations';
                $this->resumeContentType = 'list';
                $this->resumeData = [
                    'title' => 'Agent Observations',
                    'description' => 'View and manage agent observations for this campaign. This page displays recent observations of agent performance, behavior, and activities. Use this information to monitor agent quality, provide feedback, and identify areas for improvement.',
                    'stats' => [
                        'Total Observations' => $recentObservations,
                        'Recent Observations' => $this->campaign ?
                            \App\Models\Observation::whereHas('agent', function($query) {
                                $query->whereHas('campaigns', function($q) {
                                    $q->where('campaign_id', $this->campaign->id);
                                });
                            })->where('created_at', '>=', now()->subDays(7))->count() : 0,
                        'Average Rating' => $this->campaign ?
                            \App\Models\Observation::whereHas('agent', function($query) {
                                $query->whereHas('campaigns', function($q) {
                                    $q->where('campaign_id', $this->campaign->id);
                                });
                            })->avg('rating') : 0
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Recent obervations';
                $this->current_page_resume['type'] = 'recent';
                break;

            case 'campaigns.agent.report':
                if ($this->agent) {
                    $observationsCount = $this->agent->observations()->count();
                    $recentObservations = $this->agent->observations()->where('created_at', '>=', now()->subDays(30))->count();
                    $appointmentsCount = $this->agent->appointments()->count();
                    $campaignAppointments = $this->campaign ? $this->agent->appointments()->where('campaign_id', $this->campaign->id)->count() : 0;

                    $this->resumeTitle = $this->agent->getFullNameAttribute();
                    $this->resumeContentType = 'dashboard';
                    $this->resumeData = [
                        'title' => $this->agent->getFullNameAttribute() . ' - Performance Report',
                        'description' => 'Detailed performance report for ' . $this->agent->getFullNameAttribute() . '. This dashboard provides comprehensive metrics and analytics about the agent\'s performance in this campaign, including ratings, observations, appointments, and other key performance indicators.',
                        'metrics' => [
                            ['label' => 'Rating', 'value' => $this->agent->rating ?? '0', 'suffix' => '/5', 'change' => null],
                            ['label' => 'Observations', 'value' => $observationsCount, 'change' => $observationsCount > 0 && $recentObservations > 0 ? round(($recentObservations / $observationsCount) * 100) : null],
                            ['label' => 'Total Appointments', 'value' => $appointmentsCount, 'change' => null],
                            ['label' => 'Campaign Appointments', 'value' => $campaignAppointments, 'change' => $appointmentsCount > 0 ? round(($campaignAppointments / $appointmentsCount) * 100) : null]
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->agent->name;
                    $this->current_page_resume['type'] = 'chart';
                }
                break;

            case 'campaigns.reports':
                $totalCampaigns = Campaign::count();
                $activeCampaigns = Campaign::where('status', 'active')->count();
                $totalAppointments = \App\Models\Appointment::count();
                $recentAppointments = \App\Models\Appointment::where('created_at', '>=', now()->subDays(30))->count();

                $this->resumeTitle = 'Reports';
                $this->resumeContentType = auth()->user()->role_id === 6 ? 'default' : 'dashboard';
                $this->resumeData = [
                    'title' => 'Campaign Reports',
                    'description' => 'Comprehensive reporting and analytics for all campaigns. This dashboard provides detailed insights into campaign performance, agent productivity, appointment metrics, and other key performance indicators. Use these reports to evaluate campaign effectiveness and make data-driven decisions.',
                    'metrics' => [
                        ['label' => 'Active Campaigns', 'value' => $activeCampaigns, 'change' => $totalCampaigns > 0 ? round(($activeCampaigns / $totalCampaigns) * 100) : 0],
                        ['label' => 'Total Appointments', 'value' => $totalAppointments, 'change' => null],
                        ['label' => 'Recent Appointments', 'value' => $recentAppointments, 'change' => $totalAppointments > 0 ? round(($recentAppointments / $totalAppointments) * 100) : 0],
                        ['label' => 'Conversion Rate', 'value' => $totalAppointments > 0 ?
                            round((\App\Models\Appointment::where('status', 'validated')->count() / $totalAppointments) * 100) . '%' : '0%', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Rapports';
                $this->current_page_resume['type'] = auth()->user()->role_id === 6 ? 'infos' : 'chart';
                break;

            case 'campaigns.statistics':
                $totalCampaigns = Campaign::count();
                $totalAgents = User::whereIn('role_id', [5, 6])->count();
                $assignedAgents = DB::table('campaign_user')->distinct('user_id')->count('user_id');
                $averageAppointments = Campaign::has('appointments')->avg(DB::raw('(select count(*) from appointments where appointments.campaign_id = campaigns.id)'));

                $this->resumeTitle = 'Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Campaign Statistics',
                    'description' => 'Detailed statistical analysis of campaign performance and metrics. This dashboard presents key statistics about campaign effectiveness, agent productivity, appointment generation, and conversion rates. Use these insights to identify trends, compare campaign performance, and optimize marketing strategies.',
                    'metrics' => [
                        ['label' => 'Total Campaigns', 'value' => $totalCampaigns, 'change' => null],
                        ['label' => 'Agent Utilization', 'value' => $totalAgents > 0 ? round(($assignedAgents / $totalAgents) * 100) . '%' : '0%', 'change' => null],
                        ['label' => 'Avg. Appointments', 'value' => round($averageAppointments, 1), 'change' => null],
                        ['label' => 'Success Rate', 'value' => Campaign::where('status', 'completed')->count() > 0 ?
                            round((Campaign::where('status', 'completed')->where('success', true)->count() / Campaign::where('status', 'completed')->count()) * 100) . '%' : '0%', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Statistics';
                $this->current_page_resume['type'] = 'chart';
                break;

            // Customer pages
            case 'campaigns.customers.index':
                $totalCustomers = Customer::count();
                $activeCustomers = Customer::whereHas('campaigns', function($query) {
                    $query->where('status', 'active');
                })->count();
                $inactiveCustomers = $totalCustomers - $activeCustomers;
                $totalCampaigns = Campaign::count();

                $this->resumeTitle = 'Customer Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Customer Management',
                    'description' => 'Comprehensive management of campaign customers and clients. This page provides a complete overview of all customers in the system, allowing you to monitor customer relationships, campaign assignments, and key metrics. Use the filters and search functionality to find specific customers based on various criteria.',
                    'metrics' => [
                        ['label' => 'Total Customers', 'value' => $totalCustomers, 'change' => null],
                        ['label' => 'Active Customers', 'value' => $activeCustomers, 'change' => $totalCustomers > 0 ? round(($activeCustomers / $totalCustomers) * 100) : 0],
                        ['label' => 'Inactive Customers', 'value' => $inactiveCustomers, 'change' => null],
                        ['label' => 'Campaigns per Customer', 'value' => $totalCustomers > 0 ? round($totalCampaigns / $totalCustomers, 1) : 0, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Customer Management';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'campaigns.customers.create':
                $this->resumeTitle = 'Create Customer';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Customer',
                    'description' => 'Add a new customer to the system. This form allows you to create a new customer record with all necessary information including company details, contact information, billing data, and preferences. New customers can be assigned to campaigns after creation.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide complete company information and business details',
                        'Enter primary contact information for the customer',
                        'Add billing and payment information if available',
                        'Specify any special requirements or preferences',
                        'You can assign campaigns to the customer after creation',
                        'Upload any relevant customer documents if needed'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Customer';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.customers.edit':
                $customerName = $this->customer ? $this->customer->name : 'Edit Customer';

                $this->resumeTitle = 'Edit: ' . $customerName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $customerName,
                    'description' => 'Update information and settings for ' . $customerName . '. This form allows you to modify all aspects of the customer record, including company details, contact information, billing data, and preferences.',
                    'instructions' => [
                        'Update only the fields that need to be changed',
                        'You can modify company information and business details',
                        'Update contact information if there are changes',
                        'Revise billing and payment information if necessary',
                        'Adjust any special requirements or preferences',
                        'Campaign assignments can be managed separately',
                        'You can update or add customer documents if needed'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->customer ? $this->customer->name : 'Edit Customer';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.customers.show':
                $customerName = $this->customer ? $this->customer->name : 'Customer Details';
                $campaignCount = $this->customer ? $this->customer->campaigns->count() : 0;
                $activeCampaigns = $this->customer ? $this->customer->campaigns->where('status', 'active')->count() : 0;
                $completedCampaigns = $this->customer ? $this->customer->campaigns->where('status', 'completed')->count() : 0;
                $totalAppointments = $this->customer ? \App\Models\Appointment::whereIn('campaign_id', $this->customer->campaigns->pluck('id'))->count() : 0;

                $this->resumeTitle = $customerName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $customerName,
                    'subtitle' => 'Customer Details',
                    'description' => 'Detailed information about ' . $customerName . '. This page provides comprehensive details about the customer\'s profile, associated campaigns, performance metrics, and contact information. Use this information to manage the customer relationship and monitor campaign effectiveness.',
                    'stats' => [
                        'Total Campaigns' => $campaignCount,
                        'Active Campaigns' => $activeCampaigns,
                        'Completed Campaigns' => $completedCampaigns,
                        'Total Appointments' => $totalAppointments,
                        'Contact Person' => $this->customer && $this->customer->contact_person ? $this->customer->contact_person : 'Not specified',
                        'Email' => $this->customer && $this->customer->email ? $this->customer->email : 'Not specified',
                        'Phone' => $this->customer && $this->customer->phone ? $this->customer->phone : 'Not specified',
                        'Created' => $this->customer && $this->customer->created_at ? $this->customer->created_at->format('M d, Y') : 'Unknown',
                        'Last Updated' => $this->customer && $this->customer->updated_at ? $this->customer->updated_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->customer ? $this->customer->name : 'Customer Details';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'campaigns.customers.delete':
                $customerName = $this->customer ? $this->customer->name : 'Delete Customer';
                $campaignCount = $this->customer ? $this->customer->campaigns->count() : 0;
                $activeCampaigns = $this->customer ? $this->customer->campaigns->where('status', 'active')->count() : 0;

                $this->resumeTitle = 'Delete: ' . $customerName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $customerName,
                    'description' => 'You are about to permanently remove the ' . $customerName . ' customer from the system. This action will delete all customer data and cannot be reversed.',
                    'warning' => 'This action cannot be undone. All customer data, including contacts and documents, will be permanently deleted. Consider deactivating the customer instead if you may need to access this information in the future.',
                    'stats' => [
                        'Total Campaigns' => $campaignCount,
                        'Active Campaigns' => $activeCampaigns,
                        'Created' => $this->customer && $this->customer->created_at ? $this->customer->created_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete: ' . $customerName;
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.customers.contacts.delete':
                $contactName = request()->route('contact') ? \App\Models\CustomerContact::find(request()->route('contact'))->name : 'Delete Contact';
                $customerName = $this->customer ? $this->customer->name : 'Customer';

                $this->resumeTitle = 'Delete Contact: ' . $contactName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete Contact: ' . $contactName,
                    'description' => 'You are about to permanently remove the contact ' . $contactName . ' from customer ' . $customerName . '. This action cannot be reversed.',
                    'warning' => 'This action cannot be undone. All contact data will be permanently deleted.',
                    'stats' => [
                        'Customer' => $customerName,
                        'Contact' => $contactName
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete Contact: ' . $contactName;
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'campaigns.customers.documents.delete':
                $documentName = request()->route('document') ? \App\Models\CustomerDocument::find(request()->route('document'))->name : 'Delete Document';
                $customerName = $this->customer ? $this->customer->name : 'Customer';

                $this->resumeTitle = 'Delete Document: ' . $documentName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete Document: ' . $documentName,
                    'description' => 'You are about to permanently remove the document ' . $documentName . ' from customer ' . $customerName . '. This action cannot be reversed.',
                    'warning' => 'This action cannot be undone. The document file will be permanently deleted from storage.',
                    'stats' => [
                        'Customer' => $customerName,
                        'Document' => $documentName
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete Document: ' . $documentName;
                $this->current_page_resume['type'] = 'infos';
                break;

            default:
                $this->resumeTitle = 'Campaigns';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Campaigns',
                    'description' => 'Comprehensive campaign management system for organizing, tracking, and optimizing call center marketing campaigns. This module allows you to create and manage campaigns, assign agents, track performance, and generate reports. Use the navigation menu to access different sections of the campaign management system.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All campaigns';
                $this->current_page_resume['type'] = 'chart';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'campaign-module',
                'title' => 'Management',
                'description' => 'Campaign management',
                'route' => 'campaigns.index',
                'section_routes' => ['campaigns.create', 'campaigns.edit', 'campaigns.delete', 'campaigns.show'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a campaign',
                        'route' => 'campaigns.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a campaign',
                        'route' => 'campaigns.edit',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a campaign',
                        'route' => 'campaigns.delete',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a campaign',
                        'route' => 'campaigns.show',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ]
                ]
            ],
            [
                'module_id' => 'campaign-module',
                'title' => 'Agents',
                'description' => 'Campaign agents',
                'route' => 'campaigns.agents',
                'section_routes' => ['campaigns.add-agents', 'campaigns.agent.observation', 'campaigns.agent.report', 'campaigns.agent.remove'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'Observe',
                        'description' => 'Observe an agent',
                        'route' => 'campaigns.agent.observation',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Observation',
                        'description' => 'Observation an agent',
                        'route' => 'campaigns.agent.report',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Remove',
                        'description' => 'Remove an agent',
                        'route' => 'campaigns.agent.remove',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                ]
            ],
            [
                'module_id' => 'campaign-module',
                'title' => 'Reports',
                'description' => 'Campaign reports',
                'route' => 'campaigns.reports',
                'section_routes' => [],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => []
            ],
            [
                'module_id' => 'campaign-module',
                'title' => 'Statistics',
                'description' => 'Campaign statistics',
                'route' => 'campaigns.statistics',
                'section_routes' => [],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => []
            ],
            [
                'module_id' => 'campaign-module',
                'title' => 'Customers',
                'description' => 'Customer management',
                'route' => 'campaigns.customers.index',
                'section_routes' => ['campaigns.customers.create', 'campaigns.customers.edit', 'campaigns.customers.show', 'campaigns.customers.delete', 'campaigns.customers.contacts.delete', 'campaigns.customers.documents.delete'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a customer',
                        'route' => 'campaigns.customers.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a customer',
                        'route' => 'campaigns.customers.edit',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a customer',
                        'route' => 'campaigns.customers.show',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a customer',
                        'route' => 'campaigns.customers.delete',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                ]
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-page', [
            'campaign' => $this->campaign,
            'agent' => $this->agent,
            'customer' => $this->customer
        ]);
    }
}
