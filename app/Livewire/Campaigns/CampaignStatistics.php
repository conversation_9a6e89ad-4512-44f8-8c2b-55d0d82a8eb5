<?php

namespace App\Livewire\Campaigns;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\Shift;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignStatistics extends Component
{
    use WithPagination;

    public string $campaignSearch = '';
    public array $campaigns = [];
    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?int $selectedCampaignId = null;
    public string $period = 'week'; // 'day', 'week', 'month', 'year'
    public array $statisticsData = [];
    public array $chartData = [];
    public array $agentPerformance = [];

    protected $queryString = [
        'campaignSearch' => ['except' => '', 'as' => 'as'],
        'selectedCampaignId' => ['except' => null, 'as' => 'campaign'],
        'period' => ['except' => 'week'],
        'startDate' => ['except' => null],
        'endDate' => ['except' => null],
    ];

    public function mount(): void
    {
        $user = Auth::user();

        // Set default date range if not provided
        if (!$this->startDate) {
            $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        }

        if (!$this->endDate) {
            $this->endDate = Carbon::now()->format('Y-m-d');
        }

        if (in_array($user->role_id, [1, 2])) {
            $this->campaigns = Campaign::all()->toArray();
        } else {
            $this->campaigns = [$user->campaign->toArray()];
            $this->selectedCampaignId = $user->campaign_id;
            $this->campaignSearch = $user->campaign->name;
        }

        // If campaign is already selected, load statistics
        if ($this->selectedCampaignId) {
            $this->loadCampaignStatistics();
        }
    }

    public function getSelectedCampaignProperty(): ?Campaign
    {
        return Campaign::with(['manager', 'agents', 'appointments' => function($query) {
                if ($this->startDate && $this->endDate) {
                    $query->whereBetween('scheduled_at', [
                        Carbon::parse($this->startDate)->startOfDay(),
                        Carbon::parse($this->endDate)->endOfDay()
                    ]);
                }
            }])
            ->when($this->selectedCampaignId, fn($q) => $q->where('id', $this->selectedCampaignId))
            ->first();
    }

    public function updatedCampaignSearch(string $value): void
    {
        if (trim($value) === '') {
            $this->selectedCampaignId = null;
            $this->statisticsData = [];
            $this->chartData = [];
            $this->agentPerformance = [];
        }
    }

    public function updatedStartDate()
    {
        if ($this->selectedCampaignId) {
            $this->loadCampaignStatistics();
        }
    }

    public function updatedEndDate()
    {
        if ($this->selectedCampaignId) {
            $this->loadCampaignStatistics();
        }
    }

    public function updatedPeriod()
    {
        if ($this->selectedCampaignId) {
            $this->loadCampaignStatistics();
        }
    }

    public function getFilteredCampaignsProperty()
    {
        $user = Auth::user();
        $term = trim($this->campaignSearch);

        $query = Campaign::query();

        if (in_array($user->role_id, [3, 6])) {
            $query->where('id', $user->campaign_id);
        }

        if ($term !== '') {
            $like = "%{$term}%";
            $query->where('name', 'like', $like);
        }

        return $query->orderBy('name')->limit(10)->get();
    }

    public function selectCampaign(int $id): void
    {
        $this->selectedCampaignId = $id;

        $campaign = collect($this->campaigns)->firstWhere('id', $id);
        $this->campaignSearch = $campaign['name'] ?? '';

        $this->loadCampaignStatistics();
    }

    /**
     * Load campaign statistics
     */
    public function loadCampaignStatistics(): void
    {
        if (!$this->selectedCampaignId) {
            return;
        }

        $startDate = Carbon::parse($this->startDate)->startOfDay();
        $endDate = Carbon::parse($this->endDate)->endOfDay();

        // Get campaign data
        $campaign = Campaign::with(['agents', 'appointments' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('scheduled_at', [$startDate, $endDate]);
        }])->find($this->selectedCampaignId);

        if (!$campaign) {
            return;
        }

        // Calculate statistics
        $totalAgents = $campaign->agents->count();
        $totalAppointments = $campaign->appointments->count();
        $validatedAppointments = $campaign->appointments->where('status', 'validated')->count();
        $rejectedAppointments = $campaign->appointments->where('status', 'rejected')->count();
        $pendingAppointments = $campaign->appointments->where('status', 'pending')->count();

        // Calculate validation rate
        $validationRate = $totalAppointments > 0
            ? round(($validatedAppointments / $totalAppointments) * 100, 2)
            : 0;

        // Calculate daily average
        $daysDiff = max(1, $startDate->diffInDays($endDate) + 1);
        $dailyAverage = $totalAgents > 0
            ? round($totalAppointments / ($daysDiff * $totalAgents), 2)
            : 0;

        // Store statistics data
        $this->statisticsData = [
            'total_agents' => $totalAgents,
            'total_appointments' => $totalAppointments,
            'validated_appointments' => $validatedAppointments,
            'rejected_appointments' => $rejectedAppointments,
            'pending_appointments' => $pendingAppointments,
            'validation_rate' => $validationRate,
            'daily_average' => $dailyAverage,
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'days' => $daysDiff,
            ],
        ];

        // Prepare chart data
        $this->prepareChartData($startDate, $endDate, $campaign->appointments);

        // Prepare agent performance data
        $this->prepareAgentPerformanceData($campaign->agents, $startDate, $endDate);
    }

    /**
     * Prepare chart data based on the selected period
     */
    private function prepareChartData(Carbon $startDate, Carbon $endDate, $appointments): void
    {
        $format = 'Y-m-d';
        $groupByFormat = 'Y-m-d';
        $labelFormat = 'd M';

        switch ($this->period) {
            case 'day':
                $groupByFormat = 'Y-m-d H';
                $labelFormat = 'H:i';
                break;
            case 'week':
                $groupByFormat = 'Y-m-d';
                $labelFormat = 'd M';
                break;
            case 'month':
                $groupByFormat = 'Y-m-d';
                $labelFormat = 'd M';
                break;
            case 'year':
                $groupByFormat = 'Y-m';
                $labelFormat = 'M Y';
                break;
        }

        // Group appointments by date
        $groupedAppointments = $appointments->groupBy(function ($appointment) use ($groupByFormat) {
            return Carbon::parse($appointment->scheduled_at)->format($groupByFormat);
        });

        // Generate date range
        $dateRange = [];
        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            $dateKey = $currentDate->format($groupByFormat);
            $dateRange[$dateKey] = [
                'label' => $currentDate->format($labelFormat),
                'total' => 0,
                'validated' => 0,
                'rejected' => 0,
                'pending' => 0,
            ];

            if ($this->period === 'day') {
                $currentDate->addHour();
            } else if ($this->period === 'week' || $this->period === 'month') {
                $currentDate->addDay();
            } else {
                $currentDate->addMonth();
            }
        }

        // Fill in appointment counts
        foreach ($groupedAppointments as $date => $dateAppointments) {
            if (isset($dateRange[$date])) {
                $dateRange[$date]['total'] = $dateAppointments->count();
                $dateRange[$date]['validated'] = $dateAppointments->where('status', 'validated')->count();
                $dateRange[$date]['rejected'] = $dateAppointments->where('status', 'rejected')->count();
                $dateRange[$date]['pending'] = $dateAppointments->where('status', 'pending')->count();
            }
        }

        // Format chart data for ApexCharts
        $labels = [];
        $totalSeries = [];
        $validatedSeries = [];
        $rejectedSeries = [];
        $pendingSeries = [];

        foreach ($dateRange as $date => $data) {
            $labels[] = $data['label'];
            $totalSeries[] = $data['total'];
            $validatedSeries[] = $data['validated'];
            $rejectedSeries[] = $data['rejected'];
            $pendingSeries[] = $data['pending'];
        }

        $this->chartData = [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Total',
                    'data' => $totalSeries,
                ],
                [
                    'name' => 'Validated',
                    'data' => $validatedSeries,
                ],
                [
                    'name' => 'Rejected',
                    'data' => $rejectedSeries,
                ],
                [
                    'name' => 'Pending',
                    'data' => $pendingSeries,
                ],
            ],
        ];
    }

    /**
     * Prepare agent performance data
     */
    private function prepareAgentPerformanceData($agents, Carbon $startDate, Carbon $endDate): void
    {
        $this->agentPerformance = [];

        foreach ($agents as $agent) {
            // Get agent appointments
            $appointments = Appointment::where('user_id', $agent->id)
                ->whereBetween('scheduled_at', [$startDate, $endDate])
                ->get();

            $totalAppointments = $appointments->count();
            $validatedAppointments = $appointments->where('status', 'validated')->count();

            // Calculate validation rate
            $validationRate = $totalAppointments > 0
                ? round(($validatedAppointments / $totalAppointments) * 100, 2)
                : 0;

            // Calculate daily average
            $daysDiff = max(1, $startDate->diffInDays($endDate) + 1);
            $dailyAverage = round($totalAppointments / $daysDiff, 2);

            // Get presence data
            $shifts = Shift::where('user_id', $agent->id)
                ->whereBetween('start_time', [$startDate, $endDate])
                ->get();

            $totalHours = 0;
            foreach ($shifts as $shift) {
                $start = Carbon::parse($shift->start_time);
                $end = Carbon::parse($shift->end_time);
                $totalHours += $end->diffInHours($start);
            }

            $this->agentPerformance[] = [
                'id' => $agent->id,
                'name' => $agent->first_name . ' ' . $agent->last_name,
                'total_appointments' => $totalAppointments,
                'validated_appointments' => $validatedAppointments,
                'validation_rate' => $validationRate,
                'daily_average' => $dailyAverage,
                'total_hours' => $totalHours,
                'shifts_count' => $shifts->count(),
            ];
        }

        // Sort by total appointments (descending)
        usort($this->agentPerformance, function($a, $b) {
            return $b['total_appointments'] <=> $a['total_appointments'];
        });
    }

    /**
     * Export statistics data to CSV
     */
    public function exportStatisticsData()
    {
        // Implementation for exporting data will go here
        // This would typically generate a CSV file and trigger a download
        $this->dispatch('showNotification', [
            'message' => 'Export functionality will be implemented soon.',
            'type' => 'info',
        ]);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-statistics', [
            'filteredCampaigns' => $this->filteredCampaigns,
            'selectedCampaign' => $this->selectedCampaign,
        ]);
    }
}
