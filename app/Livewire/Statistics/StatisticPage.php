<?php

namespace App\Livewire\Statistics;

use App\Livewire\Global\Page;
use App\Models\Agent;
use App\Models\Campaign;
use App\Models\CallCenter;
use App\Models\User;
use App\Models\Site;
use App\Models\Platform;
use App\Models\Payment;
use Illuminate\Support\Facades\DB;

class StatisticPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Statistics';
        $this->resumeDescription = 'Performance metrics and analytics';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Statistics',
            'description' => 'Performance metrics and analytics'
        ];

        switch ($routeName) {
            case 'statistics.day':
                $today = date('d M Y');
                $activeAgents = User::where('status', 'actif')->count();
                $completedTasks = rand(50, 150); // Replace with actual data

                $this->resumeTitle = 'Daily Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Daily Statistics',
                    'description' => 'Today\'s Performance: ' . $today,
                    'metrics' => [
                        ['label' => 'Active Agents', 'value' => $activeAgents],
                        ['label' => 'Completed Tasks', 'value' => $completedTasks],
                        ['label' => 'Conversion Rate', 'value' => rand(15, 35), 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Daily Statistics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $today;
                $this->current_page_resume['description'] = 'Today\'s Performance';
                break;

            case 'statistics.general':
                $totalUsers = User::count();
                $totalSites = Site::count();
                $totalCallCenters = CallCenter::count();

                $this->resumeTitle = 'General Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'General Statistics',
                    'description' => 'Overview of system metrics',
                    'metrics' => [
                        ['label' => 'Total Users', 'value' => $totalUsers],
                        ['label' => 'Total Sites', 'value' => $totalSites],
                        ['label' => 'Call Centers', 'value' => $totalCallCenters]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'General Statistics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalUsers;
                $this->current_page_resume['description'] = 'Total Users';
                break;

            case 'statistics.kpi':
                $activeCampaigns = Campaign::where('status', 'active')->count();
                $totalCampaigns = Campaign::count();
                $campaignSuccessRate = $totalCampaigns > 0 ? round(($activeCampaigns / $totalCampaigns) * 100) : 0;

                $this->resumeTitle = 'KPI Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'KPI Statistics',
                    'description' => 'Key Performance Indicators',
                    'metrics' => [
                        ['label' => 'Active Campaigns', 'value' => $activeCampaigns],
                        ['label' => 'Total Campaigns', 'value' => $totalCampaigns],
                        ['label' => 'Success Rate', 'value' => $campaignSuccessRate, 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'KPI Statistics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $activeCampaigns;
                $this->current_page_resume['description'] = 'Active Campaigns';
                break;

            case 'statistics.performance':
                $activeAgents = User::where('status', 'actif')->count();
                $totalAgents = User::whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })->count();
                $avgRating = DB::table('users')->avg('rating') ?? 0;

                $this->resumeTitle = 'Performance Monitoring';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Performance Monitoring',
                    'description' => 'Agent and campaign performance metrics',
                    'metrics' => [
                        ['label' => 'Active Agents', 'value' => $activeAgents],
                        ['label' => 'Total Agents', 'value' => $totalAgents],
                        ['label' => 'Avg. Rating', 'value' => number_format($avgRating, 1), 'suffix' => '/5']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Performance Monitoring';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $activeAgents;
                $this->current_page_resume['description'] = 'Active Agents';
                break;

            case 'statistics.agent':
                $totalAgents = User::whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })->count();
                $newAgents = User::whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })->where('created_at', '>=', now()->subDays(30))->count();

                $this->resumeTitle = 'Agent Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agent Statistics',
                    'description' => 'Agent performance and metrics',
                    'metrics' => [
                        ['label' => 'Total Agents', 'value' => $totalAgents],
                        ['label' => 'New Agents', 'value' => $newAgents, 'description' => 'Last 30 days'],
                        ['label' => 'Avg. Performance', 'value' => rand(70, 95), 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Agent Statistics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalAgents;
                $this->current_page_resume['description'] = 'Total Agents';
                break;

            case 'statistics.campaign':
                $totalCampaigns = Campaign::count();
                $activeCampaigns = Campaign::where('status', 'active')->count();
                $completedCampaigns = Campaign::where('status', 'completed')->count();

                $this->resumeTitle = 'Campaign Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Campaign Statistics',
                    'description' => 'Campaign performance and metrics',
                    'metrics' => [
                        ['label' => 'Total Campaigns', 'value' => $totalCampaigns],
                        ['label' => 'Active Campaigns', 'value' => $activeCampaigns],
                        ['label' => 'Completed', 'value' => $completedCampaigns]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Campaign Statistics';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalCampaigns;
                $this->current_page_resume['description'] = 'Total Campaigns';
                break;

            default:
                $currentYear = date('Y');

                $this->resumeTitle = 'Statistics Dashboard';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Statistics Dashboard',
                    'description' => 'Overview for ' . $currentYear,
                    'metrics' => [
                        ['label' => 'Total Users', 'value' => User::count()],
                        ['label' => 'Active Campaigns', 'value' => Campaign::where('status', 'active')->count()],
                        ['label' => 'Call Centers', 'value' => CallCenter::count()]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Statistics Dashboard';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $currentYear;
                $this->current_page_resume['description'] = 'Current Year';
                break;
        }
    }
    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'title' => 'Dashboard',
                'description' => 'Statistics Dashboard',
                'route' => 'statistics.general',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4, 5],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Daily Statistics',
                'description' => 'Statistics for the current day',
                'route' => 'statistics.day',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4, 5],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'KPI Charts',
                'description' => 'Key Performance Indicators',
                'route' => 'statistics.kpi',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Performance',
                'description' => 'Performance monitoring',
                'route' => 'statistics.performance',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Agent Statistics',
                'description' => 'Agent performance and metrics',
                'route' => 'statistics.agent',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Campaign Statistics',
                'description' => 'Campaign performance and metrics',
                'route' => 'statistics.campaign',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }
    public function render()
    {
        return view('livewire.statistics.statistic-page');
    }
}
