<?php

namespace App\Livewire\Statistics;

use App\Models\Appointment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class DayStatistics extends Component
{
    // Date filters
    public $selectedDate;
    public $dateFormatted;

    // Chart data
    public $agentActivityData = [];
    public $appointmentsData = [];
    public $conversionRateData = [];

    // Statistics
    public $totalAppointments = 0;
    public $validatedAppointments = 0;
    public $canceledAppointments = 0;
    public $activeAgents = 0;
    public $conversionRate = 0;

    public function mount()
    {
        // Set default date to today
        $this->selectedDate = Carbon::now()->format('Y-m-d');
        $this->dateFormatted = Carbon::now()->format('F j, Y');

        // Load data for the selected date
        $this->loadData();
    }

    /**
     * Load data for the selected date
     */
    public function loadData()
    {
        $date = Carbon::parse($this->selectedDate);
        $this->dateFormatted = $date->format('F j, Y');

        // Load agent activity data
        $this->loadAgentActivityData($date);

        // Load appointments data
        $this->loadAppointmentsData($date);

        // Load conversion rate data
        $this->loadConversionRateData($date);

        // Calculate statistics
        $this->calculateStatistics($date);
    }

    /**
     * Load agent activity data for the selected date
     */
    private function loadAgentActivityData($date)
    {
        // Use database-agnostic approach for date functions
        $connection = config('database.default');
        $driver = config("database.connections.{$connection}.driver");

        // Get agent activity by hour
        if ($driver === 'sqlite') {
            $agentActivity = User::select(
                    DB::raw("cast(strftime('%H', created_at) as integer) as hour"),
                    DB::raw('count(*) as count')
                )
                ->whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })
                ->whereDate('created_at', $date)
                ->groupBy(DB::raw("strftime('%H', created_at)"))
                ->orderBy('hour')
                ->get()
                ->keyBy('hour')
                ->toArray();
        } else {
            $agentActivity = User::select(
                    DB::raw('HOUR(created_at) as hour'),
                    DB::raw('count(*) as count')
                )
                ->whereHas('role', function($query) {
                    $query->whereIn('name', ['Agent', 'Senior Agent']);
                })
                ->whereDate('created_at', $date)
                ->groupBy(DB::raw('HOUR(created_at)'))
                ->orderBy('hour')
                ->get()
                ->keyBy('hour')
                ->toArray();
        }

        // Format data for chart
        $hours = [];
        $counts = [];

        for ($i = 0; $i < 24; $i++) {
            $hours[] = sprintf('%02d:00', $i);
            $counts[] = isset($agentActivity[$i]) ? $agentActivity[$i]['count'] : 0;
        }

        $this->agentActivityData = json_encode([
            'categories' => $hours,
            'series' => [
                [
                    'name' => 'Active Agents',
                    'data' => $counts
                ]
            ]
        ]);
    }

    /**
     * Load appointments data for the selected date
     */
    private function loadAppointmentsData($date)
    {
        // Use database-agnostic approach for date functions
        $connection = config('database.default');
        $driver = config("database.connections.{$connection}.driver");

        // Get appointments by hour
        if ($driver === 'sqlite') {
            $appointments = Appointment::select(
                    DB::raw("cast(strftime('%H', created_at) as integer) as hour"),
                    DB::raw('count(*) as count')
                )
                ->whereDate('created_at', $date)
                ->groupBy(DB::raw("strftime('%H', created_at)"))
                ->orderBy('hour')
                ->get()
                ->keyBy('hour')
                ->toArray();
        } else {
            $appointments = Appointment::select(
                    DB::raw('HOUR(created_at) as hour'),
                    DB::raw('count(*) as count')
                )
                ->whereDate('created_at', $date)
                ->groupBy(DB::raw('HOUR(created_at)'))
                ->orderBy('hour')
                ->get()
                ->keyBy('hour')
                ->toArray();
        }

        // Format data for chart
        $hours = [];
        $counts = [];

        for ($i = 0; $i < 24; $i++) {
            $hours[] = sprintf('%02d:00', $i);
            $counts[] = isset($appointments[$i]) ? $appointments[$i]['count'] : 0;
        }

        $this->appointmentsData = json_encode([
            'categories' => $hours,
            'series' => [
                [
                    'name' => 'Appointments',
                    'data' => $counts
                ]
            ]
        ]);
    }

    /**
     * Load conversion rate data for the selected date
     */
    private function loadConversionRateData($date)
    {
        // Get conversion rate by hour (appointments / calls)
        $conversionData = [];

        // This is a placeholder - in a real implementation, you would calculate
        // the actual conversion rate based on calls and appointments
        for ($i = 0; $i < 24; $i++) {
            $conversionData[$i] = rand(5, 25); // Random data for demonstration
        }

        // Format data for chart
        $hours = [];
        $rates = [];

        for ($i = 0; $i < 24; $i++) {
            $hours[] = sprintf('%02d:00', $i);
            $rates[] = $conversionData[$i];
        }

        $this->conversionRateData = json_encode([
            'categories' => $hours,
            'series' => [
                [
                    'name' => 'Conversion Rate (%)',
                    'data' => $rates
                ]
            ]
        ]);
    }

    /**
     * Calculate statistics for the selected date
     */
    private function calculateStatistics($date)
    {
        // Count total appointments for the day
        $this->totalAppointments = Appointment::whereDate('created_at', $date)->count();

        // Count validated appointments
        $this->validatedAppointments = Appointment::whereDate('created_at', $date)
            ->where('status', 'validated')
            ->count();

        // Count canceled appointments
        $this->canceledAppointments = Appointment::whereDate('created_at', $date)
            ->where('status', 'canceled')
            ->count();

        // Count active agents
        $this->activeAgents = User::whereDate('created_at', $date)
            ->whereHas('role', function($query) {
                $query->whereIn('name', ['Agent', 'Senior Agent']);
            })
            ->where('status', 'active')
            ->count();

        // Calculate conversion rate (placeholder)
        $this->conversionRate = $this->totalAppointments > 0 ?
            round(($this->validatedAppointments / $this->totalAppointments) * 100, 1) : 0;
    }

    /**
     * Update data when date changes
     */
    public function updatedSelectedDate()
    {
        $this->loadData();
    }

    public function render()
    {
        return view('livewire.statistics.day-statistics');
    }
}
