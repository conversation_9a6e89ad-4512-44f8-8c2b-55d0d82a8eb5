<?php

namespace App\Livewire\Statistics;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class PerformanceMonitoring extends Component
{
    // Filters
    public $dateRange = 'month';
    public $startDate;
    public $endDate;
    public $selectedAgentId = null;
    public $selectedCampaignId = null;

    // Lists for filters
    public $agents = [];
    public $campaigns = [];

    // Chart data
    public $agentPerformanceData = [];
    public $campaignPerformanceData = [];
    public $performanceComparisonData = [];

    // Performance metrics
    public $topPerformers = [];
    public $lowPerformers = [];
    public $averagePerformance = 0;

    public function mount()
    {
        // Set default date range
        $this->setDateRange($this->dateRange);

        // Load agents and campaigns for filters
        $this->loadFilterOptions();

        // Load data
        $this->loadData();
    }

    /**
     * Set the date range for performance data
     */
    public function setDateRange($range)
    {
        $this->dateRange = $range;

        switch ($range) {
            case 'week':
                $this->startDate = Carbon::now()->subWeek()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'month':
                $this->startDate = Carbon::now()->subMonth()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'quarter':
                $this->startDate = Carbon::now()->subMonths(3)->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'year':
                $this->startDate = Carbon::now()->subYear()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            default:
                $this->startDate = Carbon::now()->subMonth()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
        }

        // Reload data with new date range
        $this->loadData();
    }

    /**
     * Load filter options (agents and campaigns)
     */
    private function loadFilterOptions()
    {
        // Load agents for filter
        $this->agents = User::select('id', DB::raw("CONCAT(first_name, ' ', last_name) as name"))
            ->whereHas('role', function($query) {
                $query->whereIn('name', ['Agent', 'Senior Agent']);
            })
            ->where('status', 'active')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get()
            ->toArray();

        // Load campaigns for filter
        $this->campaigns = Campaign::select('id', 'name')
            ->where('status', 'active')
            ->orderBy('name')
            ->get()
            ->toArray();
    }

    /**
     * Load all performance data
     */
    private function loadData()
    {
        // Load agent performance data
        $this->loadAgentPerformanceData();

        // Load campaign performance data
        $this->loadCampaignPerformanceData();

        // Load performance comparison data
        $this->loadPerformanceComparisonData();

        // Calculate performance metrics
        $this->calculatePerformanceMetrics();
    }

    /**
     * Load agent performance data
     */
    private function loadAgentPerformanceData()
    {
        // Get agent performance data
        // This is a placeholder - in a real implementation, you would calculate
        // the actual performance metrics for agents from your database

        $agentData = [];

        // Generate random data for demonstration
        for ($i = 1; $i <= 10; $i++) {
            $agentData[] = [
                'name' => "Agent " . $i,
                'performance' => rand(50, 95)
            ];
        }

        // Sort by performance (descending)
        usort($agentData, function($a, $b) {
            return $b['performance'] - $a['performance'];
        });

        // Format data for chart
        $categories = array_map(function($item) {
            return $item['name'];
        }, $agentData);

        $series = [
            [
                'name' => 'Performance Score',
                'data' => array_map(function($item) {
                    return $item['performance'];
                }, $agentData)
            ]
        ];

        $this->agentPerformanceData = json_encode([
            'categories' => $categories,
            'series' => $series
        ]);
    }

    /**
     * Load campaign performance data
     */
    private function loadCampaignPerformanceData()
    {
        // Get campaign performance data over time
        // This is a placeholder - in a real implementation, you would calculate
        // the actual performance metrics for campaigns from your database

        $interval = $this->dateRange === 'week' ? 'day' : ($this->dateRange === 'year' ? 'month' : 'week');
        $format = $interval === 'day' ? 'Y-m-d' : ($interval === 'month' ? 'Y-m' : 'Y-W');
        $displayFormat = $interval === 'day' ? 'M d' : ($interval === 'month' ? 'M Y' : 'W');

        $start = clone $this->startDate;
        $end = clone $this->endDate;

        $categories = [];
        $campaignData = [];

        // Generate data for 3 campaigns
        for ($i = 1; $i <= 3; $i++) {
            $data = [];
            $current = clone $start;

            while ($current <= $end) {
                if (!in_array($current->format($displayFormat), $categories)) {
                    $categories[] = $current->format($displayFormat);
                }

                // Generate random data for demonstration
                $data[] = rand(50, 95);

                // Increment by interval
                if ($interval === 'day') {
                    $current->addDay();
                } elseif ($interval === 'week') {
                    $current->addWeek();
                } else {
                    $current->addMonth();
                }
            }

            $campaignData[] = [
                'name' => "Campaign " . $i,
                'data' => $data
            ];
        }

        $this->campaignPerformanceData = json_encode([
            'categories' => $categories,
            'series' => $campaignData
        ]);
    }

    /**
     * Load performance comparison data
     */
    private function loadPerformanceComparisonData()
    {
        // Get performance comparison data across different metrics
        // This is a placeholder - in a real implementation, you would calculate
        // the actual performance metrics from your database

        $categories = ['Calls Made', 'Appointments Set', 'Conversion Rate', 'Call Quality', 'Customer Satisfaction'];

        // Generate data for comparison
        $series = [];

        // If agent is selected
        if ($this->selectedAgentId) {
            $series[] = [
                'name' => 'Selected Agent',
                'data' => [rand(60, 90), rand(60, 90), rand(60, 90), rand(60, 90), rand(60, 90)]
            ];
        }

        // If campaign is selected
        if ($this->selectedCampaignId) {
            $series[] = [
                'name' => 'Selected Campaign',
                'data' => [rand(60, 90), rand(60, 90), rand(60, 90), rand(60, 90), rand(60, 90)]
            ];
        }

        // Always include average
        $series[] = [
            'name' => 'Average',
            'data' => [65, 60, 70, 65, 75]
        ];

        $this->performanceComparisonData = json_encode([
            'categories' => $categories,
            'series' => $series
        ]);
    }

    /**
     * Calculate performance metrics
     */
    private function calculatePerformanceMetrics()
    {
        // Calculate top performers
        $this->topPerformers = [];

        // Generate random data for demonstration
        for ($i = 1; $i <= 3; $i++) {
            $this->topPerformers[] = [
                'name' => "Agent " . $i,
                'performance' => rand(85, 95),
                'appointments' => rand(20, 40),
                'conversion' => rand(70, 90)
            ];
        }

        // Calculate low performers
        $this->lowPerformers = [];

        // Generate random data for demonstration
        for ($i = 8; $i <= 10; $i++) {
            $this->lowPerformers[] = [
                'name' => "Agent " . $i,
                'performance' => rand(50, 65),
                'appointments' => rand(5, 15),
                'conversion' => rand(40, 60)
            ];
        }

        // Calculate average performance
        $this->averagePerformance = rand(70, 80);
    }

    /**
     * Update data when filters change
     */
    public function updatedDateRange()
    {
        $this->setDateRange($this->dateRange);
    }

    public function updatedSelectedAgentId()
    {
        $this->loadData();
    }

    public function updatedSelectedCampaignId()
    {
        $this->loadData();
    }

    public function render()
    {
        return view('livewire.statistics.performance-monitoring');
    }
}
