<?php

namespace App\Livewire\Statistics;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class KpiCharts extends Component
{
    // Date filters
    public $dateRange = 'month';
    public $startDate;
    public $endDate;

    // Chart data
    public $campaignSuccessData = [];
    public $agentPerformanceData = [];
    public $kpiTrendsData = [];

    // KPI metrics
    public $appointmentsPerAgent = 0;
    public $conversionRate = 0;
    public $averageCallDuration = 0;
    public $customerSatisfaction = 0;

    public function mount()
    {
        // Set default date range
        $this->setDateRange($this->dateRange);

        // Load data
        $this->loadData();
    }

    /**
     * Set the date range for KPI data
     */
    public function setDateRange($range)
    {
        $this->dateRange = $range;

        switch ($range) {
            case 'week':
                $this->startDate = Carbon::now()->subWeek()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'month':
                $this->startDate = Carbon::now()->subMonth()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'quarter':
                $this->startDate = Carbon::now()->subMonths(3)->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'year':
                $this->startDate = Carbon::now()->subYear()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            default:
                $this->startDate = Carbon::now()->subMonth()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
        }

        // Reload data with new date range
        $this->loadData();
    }

    /**
     * Load all KPI data
     */
    private function loadData()
    {
        // Load campaign success data
        $this->loadCampaignSuccessData();

        // Load agent performance data
        $this->loadAgentPerformanceData();

        // Load KPI trends data
        $this->loadKpiTrendsData();

        // Calculate KPI metrics
        $this->calculateKpiMetrics();
    }

    /**
     * Load campaign success rate data
     */
    private function loadCampaignSuccessData()
    {
        // Get campaign success data (appointments / targets)
        // This is a placeholder - in a real implementation, you would calculate
        // the actual success rates based on campaign targets and appointments
        $campaignData = [
            ['label' => 'Successful', 'value' => 65],
            ['label' => 'In Progress', 'value' => 25],
            ['label' => 'Failed', 'value' => 10]
        ];

        $this->campaignSuccessData = json_encode($campaignData);
    }

    /**
     * Load agent performance data
     */
    private function loadAgentPerformanceData()
    {
        // Get agent performance data across different metrics
        // This is a placeholder - in a real implementation, you would calculate
        // the actual performance metrics for agents

        $categories = ['Calls Made', 'Appointments Set', 'Conversion Rate', 'Call Quality', 'Customer Satisfaction'];

        $series = [
            [
                'name' => 'Top Performers',
                'data' => [85, 75, 80, 90, 85]
            ],
            [
                'name' => 'Average',
                'data' => [65, 55, 60, 70, 65]
            ],
            [
                'name' => 'Low Performers',
                'data' => [45, 35, 40, 50, 45]
            ]
        ];

        $this->agentPerformanceData = json_encode([
            'categories' => $categories,
            'series' => $series
        ]);
    }

    /**
     * Load KPI trends data over time
     */
    private function loadKpiTrendsData()
    {
        // Get KPI trends over time
        // This is a placeholder - in a real implementation, you would calculate
        // the actual KPI trends from your database

        $interval = $this->dateRange === 'week' ? 'day' : ($this->dateRange === 'year' ? 'month' : 'week');
        $format = $interval === 'day' ? 'Y-m-d' : ($interval === 'month' ? 'Y-m' : 'Y-W');
        $displayFormat = $interval === 'day' ? 'M d' : ($interval === 'month' ? 'M Y' : 'W');

        $start = clone $this->startDate;
        $end = clone $this->endDate;

        $categories = [];
        $appointmentsData = [];
        $conversionData = [];
        $satisfactionData = [];

        $current = clone $start;

        while ($current <= $end) {
            $categories[] = $current->format($displayFormat);

            // Generate random data for demonstration
            $appointmentsData[] = rand(50, 100);
            $conversionData[] = rand(40, 90);
            $satisfactionData[] = rand(60, 95);

            // Increment by interval
            if ($interval === 'day') {
                $current->addDay();
            } elseif ($interval === 'week') {
                $current->addWeek();
            } else {
                $current->addMonth();
            }
        }

        $this->kpiTrendsData = json_encode([
            'categories' => $categories,
            'series' => [
                [
                    'name' => 'Appointments',
                    'data' => $appointmentsData
                ],
                [
                    'name' => 'Conversion Rate',
                    'data' => $conversionData
                ],
                [
                    'name' => 'Satisfaction',
                    'data' => $satisfactionData
                ]
            ]
        ]);
    }

    /**
     * Calculate KPI metrics
     */
    private function calculateKpiMetrics()
    {
        // Calculate appointments per agent
        $totalAgents = User::whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('role', function($query) {
                $query->whereIn('name', ['Agent', 'Senior Agent']);
            })
            ->where('status', 'active')
            ->count();

        $totalAppointments = Appointment::whereBetween('created_at', [$this->startDate, $this->endDate])
            ->count();

        $this->appointmentsPerAgent = $totalAgents > 0 ?
            round($totalAppointments / $totalAgents, 1) : 0;

        // Calculate conversion rate
        $validatedAppointments = Appointment::whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', 'validated')
            ->count();

        $this->conversionRate = $totalAppointments > 0 ?
            round(($validatedAppointments / $totalAppointments) * 100, 1) : 0;

        // Placeholder values for metrics that might not be directly available
        $this->averageCallDuration = rand(180, 300); // 3-5 minutes in seconds
        $this->customerSatisfaction = rand(75, 95); // 75-95%
    }

    /**
     * Update data when date range changes
     */
    public function updatedDateRange()
    {
        $this->setDateRange($this->dateRange);
    }

    public function render()
    {
        return view('livewire.statistics.kpi-charts');
    }
}
