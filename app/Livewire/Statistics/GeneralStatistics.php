<?php

namespace App\Livewire\Statistics;

use App\Models\Campaign;
use App\Models\CallCenter;
use App\Models\User;
use App\Models\Site;
use App\Models\Platform;
use App\Models\Payment;
use App\Models\Agent;
use App\Models\Role;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Carbon\Carbon;

class GeneralStatistics extends Component
{
    // Basic statistics
    public $totalUsers;
    public $totalAgents;
    public $totalCampaigns;
    public $activeCampaigns;
    public $totalSites;
    public $totalCallCenters;
    public $totalPlatforms;
    public $totalPayments;
    public $growthPercentage;

    // Chart data
    public $usersByRole = [];
    public $usersByStatus = [];
    public $campaignsByStatus = [];
    public $usersByMonth = [];
    public $usersByMonthChartData = [];
    public $usersByRoleChartData = [];
    public $usersByStatusChartData = [];
    public $campaignsByStatusChartData = [];

    // Filters
    public $dateRange = 'year';
    public $startDate;
    public $endDate;

    public function mount()
    {
        // Set default date range
        $this->setDateRange($this->dateRange);

        // Load all statistics
        $this->loadBasicStatistics();
        $this->loadChartData();
    }

    /**
     * Set the date range for statistics
     */
    public function setDateRange($range)
    {
        $this->dateRange = $range;

        switch ($range) {
            case 'week':
                $this->startDate = Carbon::now()->subWeek()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'month':
                $this->startDate = Carbon::now()->subMonth()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'quarter':
                $this->startDate = Carbon::now()->subMonths(3)->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
            case 'year':
            default:
                $this->startDate = Carbon::now()->subYear()->startOfDay();
                $this->endDate = Carbon::now()->endOfDay();
                break;
        }

        // Reload statistics with new date range
        $this->loadBasicStatistics();
        $this->loadChartData();
    }

    /**
     * Load basic statistics
     */
    private function loadBasicStatistics()
    {
        // Count total users
        $this->totalUsers = User::count();

        // Count total agents
        $this->totalAgents = User::whereHas('role', function($query) {
            $query->whereIn('name', ['Agent', 'Senior Agent']);
        })->count();

        // Count total and active campaigns
        $this->totalCampaigns = Campaign::count();
        $this->activeCampaigns = Campaign::where('status', 'active')->count();

        // Count other entities
        $this->totalSites = Site::count();
        $this->totalCallCenters = CallCenter::count();
        $this->totalPlatforms = Platform::count();

        // Calculate total payments
        $this->totalPayments = Payment::sum('amount');

        // Calculate growth percentage (comparing to previous period)
        $previousPeriodStart = (clone $this->startDate)->subDays($this->startDate->diffInDays($this->endDate));
        $previousPeriodEnd = (clone $this->startDate)->subDay();

        $currentPeriodUsers = User::whereBetween('created_at', [$this->startDate, $this->endDate])->count();
        $previousPeriodUsers = User::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])->count();

        if ($previousPeriodUsers > 0) {
            $this->growthPercentage = round((($currentPeriodUsers - $previousPeriodUsers) / $previousPeriodUsers) * 100, 1);
        } else {
            $this->growthPercentage = $currentPeriodUsers > 0 ? 100 : 0;
        }
    }

    /**
     * Load chart data
     */
    private function loadChartData()
    {
        // Users by role
        $this->usersByRole = User::select('roles.name as role', DB::raw('count(*) as count'))
            ->join('roles', 'users.role_id', '=', 'roles.id')
            ->groupBy('roles.name')
            ->get()
            ->toArray();

        $this->usersByRoleChartData = json_encode($this->usersByRole);

        // Users by status
        $this->usersByStatus = User::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->toArray();

        $this->usersByStatusChartData = json_encode($this->usersByStatus);

        // Campaigns by status
        $this->campaignsByStatus = Campaign::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->toArray();

        $this->campaignsByStatusChartData = json_encode($this->campaignsByStatus);

        // Users by month
        $this->loadUsersByMonthData();
    }

    /**
     * Load users by month data based on the selected date range
     */
    private function loadUsersByMonthData()
    {
        $format = 'Y-m-d';
        $groupBy = 'date';

        if ($this->dateRange === 'year') {
            $format = 'Y-m';
            $groupBy = 'month';
        }

        // Use database-agnostic approach for date functions
        $connection = config('database.default');
        $driver = config("database.connections.{$connection}.driver");

        if ($driver === 'sqlite') {
            // SQLite date formatting
            $sqliteFormat = ($format === 'Y-m-d') ? '%Y-%m-%d' : '%Y-%m';

            $this->usersByMonth = User::select(
                    DB::raw("strftime('{$sqliteFormat}', created_at) as {$groupBy}"),
                    DB::raw('count(*) as count')
                )
                ->whereBetween('created_at', [$this->startDate, $this->endDate])
                ->groupBy($groupBy)
                ->orderBy($groupBy)
                ->get()
                ->toArray();
        } else {
            // MySQL date formatting
            $this->usersByMonth = User::select(
                    DB::raw("DATE_FORMAT(created_at, '{$format}') as {$groupBy}"),
                    DB::raw('count(*) as count')
                )
                ->whereBetween('created_at', [$this->startDate, $this->endDate])
                ->groupBy($groupBy)
                ->orderBy($groupBy)
                ->get()
                ->toArray();
        }

        // Format data for chart
        $labels = [];
        $data = [];

        foreach ($this->usersByMonth as $item) {
            $labels[] = $item[$groupBy];
            $data[] = $item['count'];
        }

        $this->usersByMonthChartData = json_encode([
            'categories' => $labels,
            'series' => [
                [
                    'name' => 'New Users',
                    'data' => $data
                ]
            ]
        ]);
    }

    /**
     * Update statistics when date range changes
     */
    public function updatedDateRange()
    {
        $this->setDateRange($this->dateRange);
    }

    public function render()
    {
        return view('livewire.statistics.general-statistics');
    }
}
