<?php

namespace App\Livewire\Reports;

use App\Livewire\Global\Page;
use App\Models\Report;
use Livewire\Attributes\On;

class ReportPage extends Page
{
    public ?Report $report = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-report-index')]
    public function toReportIndex(Report $report)
    {
        return $this->redirect(route('reports.index'), navigate: true);
    }

    #[On('to-report-show')]
    public function toReportShow(Report $report)
    {
        return $this->redirect(route('reports.show', ['report' => $report], ), navigate: true);
    }

    #[On('to-report-edit')]
    public function toReportEdit(Report $report)
    {
        return $this->redirect(route('reports.edit', ['report' => $report], ), navigate: true);
    }

    #[On('to-report-delete')]
    public function toReportDelete(Report $report)
    {
        return $this->redirect(route('reports.delete', ['report' => $report], ), navigate: true);
    }

    #[On('to-report-create')]
    public function toReportCreate()
    {
        return $this->redirect(route('reports.create'), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Reports';
        $this->resumeDescription = 'Report management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Reports',
            'description' => 'Report management system'
        ];

        switch ($routeName) {
            case 'reports.index':
                $totalReports = Report::count();
                $recentReports = Report::where('created_at', '>=', now()->subDays(7))->count();

                $this->resumeTitle = 'All Reports';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'All Reports',
                    'metrics' => [
                        ['label' => 'Total Reports', 'value' => $totalReports],
                        ['label' => 'Recent Reports', 'value' => $recentReports, 'description' => 'Last 7 days']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All reports';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'reports.create':
                $this->resumeTitle = 'New Report';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'New Report',
                    'description' => 'Create a new report',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide detailed information for accurate reporting',
                        'Select appropriate categories and tags'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'New report';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'reports.show':
                if ($this->report) {
                    $creatorName = $this->report->creator->getFullNameAttribute();

                    $this->resumeTitle = $creatorName;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $creatorName,
                        'subtitle' => 'Report Details',
                        'stats' => [
                            'Created' => $this->report->created_at ? $this->report->created_at->format('M d, Y') : 'Unknown',
                            'Status' => ucfirst($this->report->status ?? 'Unknown'),
                            'Type' => ucfirst($this->report->type ?? 'General')
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $creatorName;
                    $this->current_page_resume['type'] = 'chart';
                }
                break;

            case 'reports.edit':
                if ($this->report) {
                    $creatorName = $this->report->creator->getFullNameAttribute();

                    $this->resumeTitle = 'Edit: ' . $creatorName;
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit: ' . $creatorName,
                        'description' => 'Update report information',
                        'instructions' => [
                            'Update the necessary fields',
                            'You can modify report details and status'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $creatorName;
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            case 'reports.delete':
                if ($this->report) {
                    $creatorName = $this->report->creator->getFullNameAttribute();

                    $this->resumeTitle = 'Delete: ' . $creatorName;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => 'Delete: ' . $creatorName,
                        'description' => 'Are you sure you want to delete this report?',
                        'warning' => 'This action cannot be undone.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $creatorName;
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            default:
                $this->resumeTitle = 'Reports';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Reports',
                    'description' => 'Report management system'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All reports';
                $this->current_page_resume['type'] = 'chart';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'report-module',
                'title' => 'Management',
                'description' => 'Report management',
                'route' => 'reports.index',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => ['reports.create', 'reports.edit', 'reports.delete', 'reports.show'],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a report',
                        'route' => 'reports.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a report',
                        'route' => 'reports.edit',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a report',
                        'route' => 'reports.delete',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a report',
                        'route' => 'reports.show',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ]
                ]
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        if ($this->report) {
            return view('livewire.reports.report-page');
        } else {
            return view('livewire.reports.report-page', [
                'report' => $this->report,
            ]);
        }

    }
}
