<?php

namespace App\Livewire\Training;

use App\Livewire\Global\Page;
use App\Models\User;
use App\Models\Training;
use App\Models\TrainingSession;
use App\Models\TrainingModule;
use App\Traits\HandlePageExpiration;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\DB;

class TrainingPage extends Page
{
    use HandlePageExpiration;
    public ?User $agent = null;
    public ?Training $training = null;
    public ?TrainingSession $session = null;
    public ?TrainingModule $module = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_section = [];
    public array $current_page_resume = []; // For backward compatibility

    #[On('to-agent-create')]
    public function toAgentCreate()
    {
        return $this->redirect(route('agents.create'), navigate: true);
    }

    #[On('to-training-index')]
    public function toTrainingIndex()
    {
        return $this->redirect(route('training.index'), navigate: true);
    }

    #[On('to-training-agents')]
    public function toTrainingAgents()
    {
        return $this->redirect(route('training.agents.index'), navigate: true);
    }

    #[On('to-training-validation')]
    public function toTrainingValidation(User $agent)
    {
        return $this->redirect(route('training.validation.index', ['agent' => $agent]), navigate: true);
    }

    #[On('to-training-observation')]
    public function toTrainingObservation(User $agent)
    {
        return $this->redirect(route('training.observation.index', ['agent' => $agent]), navigate: true);
    }

    #[On('to-training-report')]
    public function toTrainingReport(User $agent)
    {
        return $this->redirect(route('training.report.index', ['agent' => $agent]), navigate: true);
    }

    #[On('to-training-remove')]
    public function toTrainingRemove(User $agent)
    {
        return $this->redirect(route('training.remove.index', ['agent' => $agent]), navigate: true);
    }

    #[On('to-training-modules')]
    public function toTrainingModules()
    {
        return $this->redirect(route('training.modules.index'), navigate: true);
    }

    #[On('to-training-module-create')]
    public function toTrainingModuleCreate()
    {
        return $this->redirect(route('training.modules.create'), navigate: true);
    }

    #[On('to-training-module-show')]
    public function toTrainingModuleShow($module)
    {
        return $this->redirect(route('training.modules.show', ['module' => $module]), navigate: true);
    }

    #[On('to-training-module-edit')]
    public function toTrainingModuleEdit($module)
    {
        return $this->redirect(route('training.modules.edit', ['module' => $module]), navigate: true);
    }

    #[On('to-training-module-delete')]
    public function toTrainingModuleDelete($module)
    {
        return $this->redirect(route('training.modules.delete', ['module' => $module]), navigate: true);
    }

    #[On('to-training-statistics')]
    public function toTrainingStatistics()
    {
        return $this->redirect(route('training.statistics'), navigate: true);
    }

    #[On('to-training-sessions')]
    public function toTrainingSessions()
    {
        return $this->redirect(route('training.sessions.index'), navigate: true);
    }

    #[On('to-training-session-create')]
    public function toTrainingSessionCreate()
    {
        return $this->redirect(route('training.sessions.create'), navigate: true);
    }

    #[On('to-training-session-show')]
    public function toTrainingSessionShow(TrainingSession $session)
    {
        return $this->redirect(route('training.sessions.show', ['session' => $session]), navigate: true);
    }

    #[On('to-training-session-edit')]
    public function toTrainingSessionEdit(TrainingSession $session)
    {
        return $this->redirect(route('training.sessions.edit', ['session' => $session]), navigate: true);
    }

    #[On('to-training-session-delete')]
    public function toTrainingSessionDelete(TrainingSession $session)
    {
        return $this->redirect(route('training.sessions.delete', ['session' => $session]), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Training';
        $this->resumeDescription = 'Training management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Training',
            'description' => 'Comprehensive agent training management system for developing call center talent. This module allows you to create and manage training sessions, curriculum modules, agent progress tracking, and performance evaluation.'
        ];

        switch ($routeName) {
            case 'training.index':
                $totalAgentsInTraining = User::role('agent_in_training')->count();
                $totalSessions = TrainingSession::count();
                $activeSessions = TrainingSession::where('status', 'active')->count();
                $totalModules = TrainingModule::count();
                $recentlyValidated = User::role('agent')
                    ->where('updated_at', '>=', now()->subDays(30))
                    ->count();

                $this->resumeTitle = 'Training Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Training Management',
                    'description' => 'Comprehensive overview of the agent training program. This dashboard provides key metrics and insights into training operations, allowing you to monitor agent progress, session status, and overall program effectiveness. Use this information to manage all aspects of the training program and ensure agents are properly prepared for campaign assignments.',
                    'metrics' => [
                        ['label' => 'Agents in Training', 'value' => $totalAgentsInTraining, 'change' => null],
                        ['label' => 'Active Sessions', 'value' => $activeSessions, 'change' => $totalSessions > 0 ? round(($activeSessions / $totalSessions) * 100) : 0],
                        ['label' => 'Total Modules', 'value' => $totalModules, 'change' => null],
                        ['label' => 'Recently Validated', 'value' => $recentlyValidated, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Training Management';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.sessions.index':
                $activeSessions = TrainingSession::where('status', 'active')->count();
                $totalSessions = TrainingSession::count();
                $inactiveSessions = $totalSessions - $activeSessions;
                $upcomingSessions = TrainingSession::where('start_date', '>', now())->count();
                $completedSessions = TrainingSession::where('status', 'completed')->count();

                $this->resumeTitle = 'Training Sessions';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Training Sessions',
                    'description' => 'Manage all training sessions in the system. This page provides a complete overview of training sessions, allowing you to monitor session status, schedule, module assignments, and agent participation. Use the filters and search functionality to find specific sessions based on various criteria.',
                    'metrics' => [
                        ['label' => 'Active Sessions', 'value' => $activeSessions, 'change' => $totalSessions > 0 ? round(($activeSessions / $totalSessions) * 100) : 0],
                        ['label' => 'Total Sessions', 'value' => $totalSessions, 'change' => null],
                        ['label' => 'Upcoming Sessions', 'value' => $upcomingSessions, 'change' => null],
                        ['label' => 'Completed Sessions', 'value' => $completedSessions, 'change' => $totalSessions > 0 ? round(($completedSessions / $totalSessions) * 100) : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Training Sessions';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.sessions.create':
                $availableModules = TrainingModule::where('status', 'active')->count();

                $this->resumeTitle = 'Create Training Session';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Training Session',
                    'description' => 'Create a new training session in the system. This form allows you to set up a structured training period with all necessary information including name, description, schedule, module assignments, and operational parameters.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide a descriptive name and clear objectives for the session',
                        'Set the session start and end dates to define the training period',
                        'Select the session status (planned/active/completed)',
                        'Assign training modules to create the curriculum',
                        'Specify any special requirements or resources needed',
                        'You can assign agents to the session after creation'
                    ],
                    'stats' => [
                        'Available Modules' => $availableModules,
                        'Current Sessions' => TrainingSession::where('status', 'active')->count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Training Session';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.sessions.show':
                $sessionName = $this->session ? $this->session->name : 'Session Details';
                $moduleCount = $this->session ? $this->session->modules->count() : 0;
                $agentCount = $this->session ? $this->session->agents()->count() : 0;
                $sessionDuration = $this->session && $this->session->start_date && $this->session->end_date ?
                    $this->session->start_date->diffInDays($this->session->end_date) + 1 : 'Not set';
                $completionPercentage = $this->session && $this->session->progress ? $this->session->progress : 0;

                $this->resumeTitle = $sessionName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $sessionName,
                    'subtitle' => 'Session Details',
                    'description' => 'Detailed information about the ' . $sessionName . ' training session. This page provides comprehensive details about the session\'s configuration, schedule, assigned modules, participating agents, and progress metrics. Use this information to monitor and manage all aspects of the training session.',
                    'stats' => [
                        'Status' => $this->session ? ucfirst($this->session->status) : 'Unknown',
                        'Start Date' => $this->session && $this->session->start_date ? $this->session->start_date->format('M d, Y') : 'Not set',
                        'End Date' => $this->session && $this->session->end_date ? $this->session->end_date->format('M d, Y') : 'Not set',
                        'Duration' => $sessionDuration . ' days',
                        'Modules' => $moduleCount,
                        'Agents' => $agentCount,
                        'Completion' => $completionPercentage . '%',
                        'Created' => $this->session && $this->session->created_at ? $this->session->created_at->format('M d, Y') : 'Unknown',
                        'Last Updated' => $this->session && $this->session->updated_at ? $this->session->updated_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->session ? $this->session->name : 'Session Details';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.sessions.edit':
                $sessionName = $this->session ? $this->session->name : 'Session';
                $moduleCount = $this->session ? $this->session->modules->count() : 0;
                $agentCount = $this->session ? $this->session->agents()->count() : 0;

                $this->resumeTitle = 'Edit: ' . $sessionName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $sessionName,
                    'description' => 'Update information and settings for the ' . $sessionName . ' training session. This form allows you to modify all aspects of the session configuration, including name, description, schedule, module assignments, and operational parameters.',
                    'instructions' => [
                        'Update only the fields that need to be changed',
                        'You can modify the session name, description, and objectives',
                        'Adjust the session dates to extend or shorten the training period',
                        'Change the session status to reflect its current state',
                        'Add or remove modules to update the curriculum',
                        'Update any special requirements or resources',
                        'Agent assignments can be managed separately'
                    ],
                    'stats' => [
                        'Current Modules' => $moduleCount,
                        'Assigned Agents' => $agentCount
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->session ? 'Edit: ' . $this->session->name : 'Edit Session';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.sessions.delete':
                $sessionName = $this->session ? $this->session->name : 'Session';
                $moduleCount = $this->session ? $this->session->modules->count() : 0;
                $agentCount = $this->session ? $this->session->agents()->count() : 0;

                $this->resumeTitle = 'Delete: ' . $sessionName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $sessionName,
                    'description' => 'You are about to permanently remove the ' . $sessionName . ' training session from the system. This action will delete all session data and cannot be reversed.',
                    'warning' => 'This action cannot be undone. All session data, including module assignments and agent progress records, will be permanently deleted. Consider deactivating the session instead if you may need to access this information in the future.',
                    'stats' => [
                        'Status' => $this->session ? ucfirst($this->session->status) : 'Unknown',
                        'Modules' => $moduleCount,
                        'Agents' => $agentCount,
                        'Created' => $this->session && $this->session->created_at ? $this->session->created_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->session ? 'Delete: ' . $this->session->name : 'Delete Session';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.agents.index':
                $totalAgentsInTraining = User::role('agent_in_training')->count();
                $newAgents = User::role('agent_in_training')->where('created_at', '>=', now()->subDays(30))->count();
                $agentsWithSessions = User::role('agent_in_training')->whereHas('trainingSessions')->count();
                $agentsWithObservations = User::role('agent_in_training')->whereHas('observations')->count();
                $averageRating = User::role('agent_in_training')->whereNotNull('rating')->avg('rating') ?? 0;

                $this->resumeTitle = 'Agents in Training';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agents in Training',
                    'description' => 'Comprehensive management of agents in the training program. This page provides a complete overview of all agents currently undergoing training, allowing you to monitor their progress, performance, and readiness for campaign assignment. Use the filters and search functionality to find specific agents based on various criteria.',
                    'metrics' => [
                        ['label' => 'Total Agents', 'value' => $totalAgentsInTraining, 'change' => null],
                        ['label' => 'New Agents', 'value' => $newAgents, 'change' => $totalAgentsInTraining > 0 ? round(($newAgents / $totalAgentsInTraining) * 100) : 0],
                        ['label' => 'In Sessions', 'value' => $agentsWithSessions, 'change' => $totalAgentsInTraining > 0 ? round(($agentsWithSessions / $totalAgentsInTraining) * 100) : 0],
                        ['label' => 'Avg. Rating', 'value' => number_format($averageRating, 1), 'suffix' => '/5', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Agents in Training';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.validation.index':
                $agentName = $this->agent ? $this->agent->getFullNameAttribute() : 'Agent';
                $observationsCount = $this->agent ? $this->agent->observations()->count() : 0;
                $sessionsCount = $this->agent ? $this->agent->trainingSessions()->count() : 0;
                $currentRating = $this->agent ? ($this->agent->rating ?? '0') : '0';
                $trainingDuration = $this->agent ? now()->diffInDays($this->agent->created_at) : 0;

                $this->resumeTitle = $agentName . ' - Validation';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => $agentName . ' - Validation',
                    'description' => 'Validate ' . $agentName . '\'s training completion and readiness for campaign assignment. This form allows you to review the agent\'s training performance, assign a final rating, and determine their eligibility for promotion to production status.',
                    'instructions' => [
                        'Review the agent\'s complete training history and performance metrics',
                        'Evaluate the agent\'s readiness based on observations and session completion',
                        'Assign a final rating that reflects the agent\'s overall performance',
                        'Provide detailed feedback on strengths and areas for improvement',
                        'Optionally assign the agent to a specific campaign if they are validated',
                        'Validation will change the agent\'s role from training to production status'
                    ],
                    'stats' => [
                        'Current Rating' => $currentRating . '/5',
                        'Observations' => $observationsCount,
                        'Sessions Completed' => $sessionsCount,
                        'Training Duration' => $trainingDuration . ' days'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->agent ? $this->agent->getFullNameAttribute() . ' - Validation' : 'Agent Validation';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.observation.index':
                $agentName = $this->agent ? $this->agent->getFullNameAttribute() : 'Agent';
                $previousObservations = $this->agent ? $this->agent->observations()->count() : 0;
                $currentRating = $this->agent ? ($this->agent->rating ?? '0') : '0';
                $sessionsCount = $this->agent ? $this->agent->trainingSessions()->count() : 0;

                $this->resumeTitle = $agentName . ' - Observation';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => $agentName . ' - Observation',
                    'description' => 'Record a training observation for ' . $agentName . '. This form allows you to document the agent\'s performance, behavior, and progress during a specific training activity or assessment. These observations contribute to the agent\'s overall evaluation and readiness determination.',
                    'instructions' => [
                        'Provide specific details about the observed activity or assessment',
                        'Document both positive aspects and areas needing improvement',
                        'Be objective and focus on observable behaviors and outcomes',
                        'Provide constructive feedback that will help the agent improve',
                        'Assign a rating that accurately reflects the agent\'s performance in this specific observation',
                        'Include any recommendations for additional training or focus areas'
                    ],
                    'stats' => [
                        'Current Rating' => $currentRating . '/5',
                        'Previous Observations' => $previousObservations,
                        'Sessions' => $sessionsCount
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->agent ? $this->agent->getFullNameAttribute() . ' - Observation' : 'Agent Observation';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.report.index':
                $agentName = $this->agent ? $this->agent->getFullNameAttribute() : 'Training Reports';
                $observationsCount = $this->agent ? $this->agent->observations()->count() : 0;
                $recentObservations = $this->agent ? $this->agent->observations()->where('created_at', '>=', now()->subDays(30))->count() : 0;
                $sessionsCount = $this->agent ? $this->agent->trainingSessions()->count() : 0;
                $completedModules = $this->agent ? $this->agent->completedModules()->count() : 0;
                $currentRating = $this->agent ? ($this->agent->rating ?? '0') : '0';
                $trainingProgress = $this->agent && $this->agent->progress ? $this->agent->progress : 0;

                $this->resumeTitle = $agentName . ' - Report';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => $agentName . ' - Training Report',
                    'description' => 'Comprehensive performance report for ' . $agentName . '. This dashboard provides detailed metrics and analytics about the agent\'s training progress, performance in various modules, observation ratings, and overall readiness assessment. Use this information to make informed decisions about the agent\'s development and potential validation.',
                    'metrics' => [
                        ['label' => 'Rating', 'value' => $currentRating, 'suffix' => '/5', 'change' => null],
                        ['label' => 'Progress', 'value' => $trainingProgress, 'suffix' => '%', 'change' => null],
                        ['label' => 'Observations', 'value' => $observationsCount, 'change' => $observationsCount > 0 && $recentObservations > 0 ? round(($recentObservations / $observationsCount) * 100) : null],
                        ['label' => 'Modules Completed', 'value' => $completedModules, 'change' => $sessionsCount > 0 ? round(($completedModules / $sessionsCount) * 100) : null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->agent ? $this->agent->getFullNameAttribute() . ' - Report' : 'Training Reports';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.remove.index':
                $agentName = $this->agent ? $this->agent->getFullNameAttribute() : 'Agent';
                $observationsCount = $this->agent ? $this->agent->observations()->count() : 0;
                $sessionsCount = $this->agent ? $this->agent->trainingSessions()->count() : 0;
                $currentRating = $this->agent ? ($this->agent->rating ?? '0') : '0';
                $trainingDuration = $this->agent ? now()->diffInDays($this->agent->created_at) : 0;

                $this->resumeTitle = $agentName . ' - Remove';
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $agentName . ' - Remove from Training',
                    'description' => 'You are about to remove ' . $agentName . ' from the training program. This action will update the agent\'s status and remove them from any active training sessions.',
                    'warning' => 'This action will remove the agent from the current training program. The agent\'s training history and observations will be preserved, but they will no longer be part of active training sessions. You can specify whether the agent should be recycled for future training or permanently removed.',
                    'stats' => [
                        'Current Rating' => $currentRating . '/5',
                        'Observations' => $observationsCount,
                        'Sessions' => $sessionsCount,
                        'Training Duration' => $trainingDuration . ' days'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->agent ? $this->agent->getFullNameAttribute() . ' - Remove' : 'Remove from Training';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.modules.index':
                $totalModules = TrainingModule::count();
                $activeModules = TrainingModule::where('status', 'active')->count();
                $inactiveModules = $totalModules - $activeModules;
                $averageDuration = TrainingModule::avg('duration') ?? 0;
                $sessionsUsingModules = DB::table('training_session_module')->distinct('training_session_id')->count('training_session_id');

                $this->resumeTitle = 'Training Modules';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Training Modules',
                    'description' => 'Manage training curriculum modules for agent development. This page provides a complete overview of all training modules in the system, allowing you to create, edit, and organize the educational content used in training sessions. Use the filters and search functionality to find specific modules based on various criteria.',
                    'metrics' => [
                        ['label' => 'Total Modules', 'value' => $totalModules, 'change' => null],
                        ['label' => 'Active Modules', 'value' => $activeModules, 'change' => $totalModules > 0 ? round(($activeModules / $totalModules) * 100) : 0],
                        ['label' => 'Avg. Duration', 'value' => round($averageDuration, 1), 'suffix' => ' days', 'change' => null],
                        ['label' => 'Sessions Using', 'value' => $sessionsUsingModules, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Training Modules';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.modules.create':
                $activeSessions = TrainingSession::where('status', 'active')->count();

                $this->resumeTitle = 'Create Training Module';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Training Module',
                    'description' => 'Create a new training curriculum module for agent development. This form allows you to define educational content that will be used in training sessions, including learning objectives, materials, assessments, and duration requirements.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide a descriptive name and clear learning objectives',
                        'Define detailed module content including topics and activities',
                        'Specify assessment methods and success criteria',
                        'Set the recommended duration in days',
                        'Determine the module status (active/inactive)',
                        'Include any prerequisites or dependencies on other modules',
                        'You can assign the module to training sessions after creation'
                    ],
                    'stats' => [
                        'Active Sessions' => $activeSessions,
                        'Existing Modules' => TrainingModule::count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Training Module';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.modules.show':
                $moduleName = $this->module ? $this->module->name : 'Module Details';
                $sessionsCount = $this->module ? $this->module->sessions->count() : 0;
                $activeSessions = $this->module && $this->module->sessions ? $this->module->sessions->where('status', 'active')->count() : 0;
                $completionRate = $this->module && $this->module->completion_rate ? $this->module->completion_rate : 0;

                // Check if the agent_module_completion table exists before querying it
                $agentsCompleted = 0;
                if ($this->module) {
                    try {
                        $agentsCompleted = DB::table('agent_module_completion')->where('training_module_id', $this->module->id)->count();
                    } catch (\Exception $e) {
                        // Table doesn't exist yet, leave count at 0
                    }
                }

                $this->resumeTitle = $moduleName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $moduleName,
                    'subtitle' => 'Module Details',
                    'description' => 'Detailed information about the ' . $moduleName . ' training module. This page provides comprehensive details about the module\'s content, learning objectives, usage in training sessions, and performance metrics. Use this information to monitor and manage the effectiveness of this training component.',
                    'stats' => [
                        'Status' => $this->module ? ucfirst($this->module->status) : 'Unknown',
                        'Duration' => $this->module ? $this->module->duration . ' days' : 'Not set',
                        'Sessions' => $sessionsCount . ($activeSessions > 0 ? ' (' . $activeSessions . ' active)' : ''),
                        'Completion Rate' => $completionRate . '%',
                        'Agents Completed' => $agentsCompleted,
                        'Created' => $this->module && $this->module->created_at ? $this->module->created_at->format('M d, Y') : 'Unknown',
                        'Last Updated' => $this->module && $this->module->updated_at ? $this->module->updated_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->module ? $this->module->name : 'Module Details';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'training.modules.edit':
                $moduleName = $this->module ? $this->module->name : 'Module';
                $sessionsCount = $this->module ? $this->module->sessions->count() : 0;
                $activeSessions = $this->module && $this->module->sessions ? $this->module->sessions->where('status', 'active')->count() : 0;

                $this->resumeTitle = 'Edit: ' . $moduleName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $moduleName,
                    'description' => 'Update information and content for the ' . $moduleName . ' training module. This form allows you to modify all aspects of the module configuration, including learning objectives, content, assessments, and operational parameters.',
                    'instructions' => [
                        'Update only the fields that need to be changed',
                        'You can modify the module name, description, and learning objectives',
                        'Revise content, activities, and assessment methods as needed',
                        'Adjust the recommended duration if necessary',
                        'Change the module status to reflect its current state',
                        'Update prerequisites or dependencies on other modules',
                        'Be aware that changes may affect ongoing training sessions'
                    ],
                    'stats' => [
                        'Current Duration' => $this->module ? $this->module->duration . ' days' : 'Not set',
                        'Sessions Using' => $sessionsCount,
                        'Active Sessions' => $activeSessions
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->module ? 'Edit: ' . $this->module->name : 'Edit Module';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.modules.delete':
                $moduleName = $this->module ? $this->module->name : 'Module';
                $sessionsCount = $this->module ? $this->module->sessions->count() : 0;
                $activeSessions = $this->module && $this->module->sessions ? $this->module->sessions->where('status', 'active')->count() : 0;

                // Check if the agent_module_completion table exists before querying it
                $agentsCompleted = 0;
                if ($this->module) {
                    try {
                        $agentsCompleted = DB::table('agent_module_completion')->where('training_module_id', $this->module->id)->count();
                    } catch (\Exception $e) {
                        // Table doesn't exist yet, leave count at 0
                    }
                }

                $this->resumeTitle = 'Delete: ' . $moduleName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $moduleName,
                    'description' => 'You are about to permanently remove the ' . $moduleName . ' training module from the system. This action will delete all module data and cannot be reversed.',
                    'warning' => 'This action cannot be undone. All module data, including content, assessments, and completion records, will be permanently deleted. This module will also be removed from any training sessions that use it, which may affect ongoing training. Consider deactivating the module instead if you may need to access this information in the future.',
                    'stats' => [
                        'Status' => $this->module ? ucfirst($this->module->status) : 'Unknown',
                        'Sessions Using' => $sessionsCount,
                        'Active Sessions' => $activeSessions,
                        'Agents Completed' => $agentsCompleted,
                        'Created' => $this->module && $this->module->created_at ? $this->module->created_at->format('M d, Y') : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = $this->module ? 'Delete: ' . $this->module->name : 'Delete Module';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'training.statistics':
                $totalAgentsInTraining = User::where('role_id', 5)->count();
                $totalValidatedAgents = User::where('role_id', 6)->count();
                $totalSessions = TrainingSession::count();
                $completedSessions = TrainingSession::where('status', 'completed')->count();
                $averageTrainingDuration = 0;
                $validatedAgents = User::where('role_id', 6)->get();
                if ($validatedAgents->count() > 0) {
                    $totalDays = 0;
                    foreach ($validatedAgents as $agent) {
                        if ($agent->validated_at && $agent->created_at) {
                            $totalDays += $agent->validated_at->diffInDays($agent->created_at);
                        }
                    }
                    $averageTrainingDuration = $validatedAgents->count() > 0 ? round($totalDays / $validatedAgents->count(), 1) : 0;
                }

                $this->resumeTitle = 'Training Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Training Statistics',
                    'description' => 'Detailed statistical analysis of the training program performance and metrics. This dashboard presents key statistics about agent development, session effectiveness, module completion rates, and validation outcomes. Use these insights to identify trends, evaluate program effectiveness, and optimize training strategies.',
                    'metrics' => [
                        ['label' => 'Agents in Training', 'value' => $totalAgentsInTraining, 'change' => null],
                        ['label' => 'Validated Agents', 'value' => $totalValidatedAgents, 'change' => null],
                        ['label' => 'Avg. Training Duration', 'value' => $averageTrainingDuration, 'suffix' => ' days', 'change' => null],
                        ['label' => 'Session Completion', 'value' => $totalSessions > 0 ? round(($completedSessions / $totalSessions) * 100) : 0, 'suffix' => '%', 'change' => null],
                        ['label' => 'Validation Rate', 'value' => $totalAgentsInTraining + $totalValidatedAgents > 0 ?
                            round(($totalValidatedAgents / ($totalAgentsInTraining + $totalValidatedAgents)) * 100) : 0, 'suffix' => '%', 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Training Statistics';
                $this->current_page_resume['type'] = 'chart';
                break;

            default:
                $this->resumeTitle = 'Training';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Training',
                    'description' => 'Comprehensive agent training management system for developing call center talent. This module allows you to create and manage training sessions, curriculum modules, agent progress tracking, and performance evaluation. Use the navigation menu to access different sections of the training management system.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Training';
                $this->current_page_resume['type'] = 'chart';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);

        // Get parameters from the route
        $module = request()->route('module');
        $session = request()->route('session');
        $agent = request()->route('agent');

        // Set the module if provided
        if ($module) {
            $this->module = $module;
        }

        // Set the session if provided
        if ($session) {
            $this->session = $session;
        }

        // Set the agent if provided
        if ($agent) {
            $this->agent = $agent;
        }
        $this->pages = [
            [
                'module_id' => 'training-module',
                'title' => 'Management',
                'description' => 'Training management',
                'route' => 'training.index',
                'section_routes' => ['training.validation.index', 'training.observation.index', 'training.report.index', 'training.remove.index', 'training.sessions.index', 'training.sessions.create', 'training.sessions.show', 'training.sessions.edit', 'training.sessions.delete'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'Sessions',
                        'description' => 'Manage training sessions',
                        'route' => 'training.sessions.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Create Session',
                        'description' => 'Create a new training session',
                        'route' => 'training.sessions.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Validate',
                        'description' => 'Validate an agent',
                        'route' => 'training.validation.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Observe',
                        'description' => 'Observe an agent',
                        'route' => 'training.observation.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Report',
                        'description' => 'View training reports',
                        'route' => 'training.report.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Remove',
                        'description' => 'Remove an agent from training',
                        'route' => 'training.remove.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                ],
            ],
            [
                'module_id' => 'training-module',
                'title' => 'Agents',
                'description' => 'Agents in training',
                'route' => 'training.agents.index',
                'section_routes' => [],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => []
            ],
            [
                'module_id' => 'training-module',
                'title' => 'Modules',
                'description' => 'Training modules',
                'route' => 'training.modules.index',
                'section_routes' => ['training.modules.create', 'training.modules.show', 'training.modules.edit', 'training.modules.delete'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'All Modules',
                        'description' => 'View all training modules',
                        'route' => 'training.modules.index',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Create Module',
                        'description' => 'Create a new training module',
                        'route' => 'training.modules.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ]
                ]
            ],
            [
                'module_id' => 'training-module',
                'title' => 'Statistics',
                'description' => 'Training statistics',
                'route' => 'training.statistics',
                'section_routes' => [],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => []
            ]
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = !empty($this->current_page['sections']) ? $this->getCurrentSection($this->current_page) : [];
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        // If no component is specified, redirect to training sessions
        if (empty($this->component)) {
            return redirect()->route('training.sessions.index');
        }

        return view('livewire.training.training-page', [
            'agent' => $this->agent,
            'training' => $this->training,
            'session' => $this->session,
            'module' => $this->module
        ]);
    }
}
