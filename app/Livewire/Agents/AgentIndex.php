<?php

namespace App\Livewire\Agents;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class AgentIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedAgents = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function mount()
    {
        $this->selectedAgents = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedAgents = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedAgents = $value
            ? $this->agents->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleUserSelection(int $userId)
    {
        if (in_array($userId, $this->selectedAgents)) {
            $this->selectedAgents = array_diff($this->selectedAgents, [$userId]);
        } else {
            $this->selectedAgents[] = $userId;
        }

        $currentPageIds = $this->agents->pluck('id')->all();
        $this->selectAll = !empty($this->selectedAgents) && empty(array_diff($currentPageIds, $this->selectedAgents));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedAgents)) {
            User::whereIn('id', $this->selectedAgents)->delete();
            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Utilisateurs sélectionnés supprimés avec succès !');
            $this->resetPage();
        }
    }

    protected function getAgentsQuery()
    {
        $user = auth()->user();

        $query = User::query()
            ->with(['role', 'campaign', 'training', 'training.module'])
            ->where('role_id', '=', 6) // Agent role ID is 6
            ->orderBy($this->sortField, $this->sortDirection);

        if ($user->role_id === 3) {
            $query->where('campaign_id', $user->campaign_id);
        } elseif ($user->role_id === 6) { // Agent role ID is 6
            $query->where('id', $user->id);
        }


        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getAgentsProperty()
    {
        return $this->getAgentsQuery()->paginate($this->perPage);
    }

    public function render()
    {
        $agents = $this->agents;
        return view('livewire.agents.agent-index', [
            'agents' => $agents,
        ]);
    }
}
