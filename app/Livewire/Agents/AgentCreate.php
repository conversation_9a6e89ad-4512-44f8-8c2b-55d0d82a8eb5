<?php

namespace App\Livewire\Agents;

use App\Models\Campaign;
use App\Models\Media;
use App\Models\Role;
use App\Models\Training;
use App\Models\User;
use App\Services\RegistrationNumberService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithFileUploads;

class AgentCreate extends Component
{
    use WithFileUploads;

    public $campaigns;
    public $trainers;
    public $roles;
    public $trainingModules;
    public $form = [
        'first_name' => '',
        'last_name' => '',
        'email' => '',
        'password' => '',
        'password_confirmation' => '',
        'phone_number' => '',
        'birth_date' => null,
        'address' => '',
        'city' => '',
        'country' => '',
        'status' => 'in_training', // Default to in_training for new agents
        'campaign_id' => null,
        'hire_date' => null,
        'training_completion_date' => null,
        'training_score' => null,
        'trainer_id' => null,
        'daily_appointment_target' => 0,
        'weekly_hours_target' => 0,
        'profile_picture' => null,
        'role_id' => 6, // Default to agent role (6), validated in rules
        'training_module_id' => null, // Training module ID
    ];

    // Document management
    public $resume;
    public $id_card;
    public $certificates = [];
    public $other_documents = [];

    public $documentTypes = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificates',
        'other_document' => 'Other Documents'
    ];

    public function mount()
    {
        $this->campaigns = Campaign::all();
        $this->trainers = User::where('role_id', 3)->get(); // Assuming role_id 3 is for trainers

        // Only include the agent role (role_id 6)
        $this->roles = Role::where('id', 6)->get();

        $this->trainingModules = \App\Models\TrainingModule::all();
    }

    public function save()
    {
        // Add debugging
        Log::info('Agent create save method called');

        $this->validate([
            'form.first_name' => 'required|string|max:255',
            'form.last_name' => 'required|string|max:255',
            'form.email' => 'required|email|max:255|unique:users,email',
            'form.password' => 'required|min:8|confirmed',
            'form.phone_number' => 'nullable|string|max:20',
            'form.birth_date' => 'nullable|date',
            'form.address' => 'nullable|string|max:255',
            'form.city' => 'nullable|string|max:100',
            'form.country' => 'nullable|string|max:100',
            'form.status' => 'required|string|in:in_training,active,inactive,engaged',
            'form.campaign_id' => 'nullable|exists:campaigns,id',
            'form.hire_date' => 'nullable|date',
            'form.training_completion_date' => 'nullable|date',
            'form.training_score' => 'nullable|numeric|min:0|max:100',
            'form.trainer_id' => 'nullable|exists:users,id',
            'form.daily_appointment_target' => 'nullable|integer|min:0',
            'form.weekly_hours_target' => 'nullable|numeric|min:0',
            'form.profile_picture' => 'nullable|image|mimes:jpeg,png|max:2048', // 2MB max
            'form.role_id' => 'required|exists:roles,id|in:6', // Only allow agent role (6)
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'id_card' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048', // 2MB max
            'certificates.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
            'other_documents.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:10240', // 10MB max
        ]);

        // Generate a unique registration number using the service
        $registrationNumber = RegistrationNumberService::generate(7); // 7 is the agent role ID

        // Create new agent (user with role_id 7)
        $agent = User::create([
            'first_name' => $this->form['first_name'],
            'last_name' => $this->form['last_name'],
            'email' => $this->form['email'],
            'password' => Hash::make($this->form['password']),
            'phone_number' => $this->form['phone_number'],
            'birth_date' => $this->form['birth_date'],
            'address' => $this->form['address'],
            'city' => $this->form['city'],
            'country' => $this->form['country'],
            'status' => $this->form['status'],
            'campaign_id' => $this->form['campaign_id'],
            'registration_number' => $registrationNumber,
            'hire_date' => $this->form['hire_date'],
            'training_completion_date' => $this->form['training_completion_date'],
            'training_score' => $this->form['training_score'],
            'trainer_id' => $this->form['trainer_id'],
            'daily_appointment_target' => $this->form['daily_appointment_target'],
            'weekly_hours_target' => $this->form['weekly_hours_target'],
            'role_id' => 7 // Always set to agent role (7)
        ]);

        // Handle profile picture upload
        if ($this->form['profile_picture']) {
            $filePath = $this->form['profile_picture']->store('media/profile_pictures', 'public');
            $agent->media()->create([
                'file_path' => $filePath,
                'file_name' => $this->form['profile_picture']->getClientOriginalName(),
                'mime_type' => $this->form['profile_picture']->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle resume upload
        if ($this->resume) {
            $filePath = $this->resume->store('media/documents/resumes', 'public');
            $agent->media()->create([
                'file_name' => $this->resume->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->resume->getMimeType(),
                'category' => 'resume',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle ID card upload
        if ($this->id_card) {
            $filePath = $this->id_card->store('media/documents/id_cards', 'public');
            $agent->media()->create([
                'file_name' => $this->id_card->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->id_card->getMimeType(),
                'category' => 'id_card',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle certificates upload
        if (!empty($this->certificates)) {
            foreach ($this->certificates as $certificate) {
                $filePath = $certificate->store('media/documents/certificates', 'public');
                $agent->media()->create([
                    'file_name' => $certificate->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $certificate->getMimeType(),
                    'category' => 'certificate',
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }

        // Handle other documents upload
        if (!empty($this->other_documents)) {
            foreach ($this->other_documents as $document) {
                $filePath = $document->store('media/documents/other', 'public');
                $agent->media()->create([
                    'file_name' => $document->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $document->getMimeType(),
                    'category' => 'other_document',
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }

        // Create a training record for the agent if status is in_training
        if ($this->form['status'] === 'in_training') {
            // Get the first training module if none is selected
            $moduleId = $this->form['training_module_id'] ??
                        \App\Models\TrainingModule::first()?->id ?? null;

            Training::create([
                'user_id' => $agent->id,
                'start_date' => now(),
                'progress' => 0,
                'rating' => null,
                'training_module_id' => $moduleId,
            ]);
        }

        // Add debugging
        Log::info('Agent created successfully', ['agent_id' => $agent->id]);

        session()->flash('message', 'Agent created successfully!');

        // Redirect to the training agents list since new agents should go to training first
        return $this->redirect(route('training.agents'), navigate: true);
    }

    /**
     * Remove profile picture
     */
    public function removeProfilePicture()
    {
        $this->form['profile_picture'] = null;
    }

    /**
     * Remove a certificate from the array
     */
    public function removeCertificate($index)
    {
        if (isset($this->certificates[$index])) {
            unset($this->certificates[$index]);
            $this->certificates = array_values($this->certificates);
        }
    }

    /**
     * Remove an other document from the array
     */
    public function removeOtherDocument($index)
    {
        if (isset($this->other_documents[$index])) {
            unset($this->other_documents[$index]);
            $this->other_documents = array_values($this->other_documents);
        }
    }

    public function render()
    {
        return view('livewire.agents.agent-create', [
            'documentTypes' => $this->documentTypes,
            'roles' => $this->roles,
            'trainingModules' => $this->trainingModules
        ]);
    }
}
