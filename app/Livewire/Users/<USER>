<?php

namespace App\Livewire\Users;

use App\Models\User;
use App\Models\Campaign;
use App\Models\Certification;
use App\Models\Skill;
use App\Models\Media;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class EnhancedUserShow extends Component
{
    public User $user;
    public $userDocuments = [];
    public $profilePictureUrl;
    public $userSkills = [];
    public $userCertifications = [];
    public $campaignEligibility = [];

    // Document verification
    public $documentVerification = [];
    public $selectedDocumentId = null;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadUserDocuments();
        $this->setProfilePictureUrl();
        $this->loadUserSkills();
        $this->loadUserCertifications();

        // Only calculate campaign eligibility for agents
        if ($this->user->role_id == 6) {
            $this->calculateCampaignEligibility();
            $this->loadAgentPerformanceMetrics();
        }
    }

    /**
     * Load agent performance metrics
     */
    protected function loadAgentPerformanceMetrics()
    {
        // Set default values
        $this->user->calls_count = 0;
        $this->user->appointments_count = 0;
        $this->user->rating = 0;

        // Check if relationships exist before calling them
        if (method_exists($this->user, 'calls')) {
            $this->user->calls_count = $this->user->calls()->count();
        }

        if (method_exists($this->user, 'appointments')) {
            $this->user->appointments_count = $this->user->appointments()->count();
        }

        // Load rating from training
        if ($this->user->training) {
            $this->user->rating = $this->user->training->rating ?? 0;
        }
    }

    /**
     * Set the profile picture URL
     */
    protected function setProfilePictureUrl()
    {
        // Use the User model's getProfilePictureUrl method for consistency
        $this->profilePictureUrl = $this->user->getProfilePictureUrl();
    }

    /**
     * Load user documents grouped by category
     */
    protected function loadUserDocuments()
    {
        $documents = $this->user->media()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->get();

        // Group documents by category and add proper URLs
        foreach ($documents as $document) {
            $docId = $document->id;
            $this->userDocuments[$document->category][] = [
                'id' => $docId,
                'file_name' => $document->file_name,
                'file_path' => $document->file_path,
                'document_title' => $document->title,
                'description' => $document->description,
                'created_at' => $document->created_at,
                'expiry_date' => $document->expiry_date,
                'verification_status' => $document->verification_status ?? 'pending',
                'rejection_reason' => $document->rejection_reason,
                'department' => $document->department,
                'url' => route('documents.view', ['id' => $docId])
            ];

            // Initialize verification data for each document
            if (!isset($this->documentVerification[$docId])) {
                $this->documentVerification[$docId] = [
                    'status' => $document->verification_status ?? 'verified',
                    'reason' => $document->rejection_reason ?? '',
                    'editing' => false
                ];
            }
        }
    }

    /**
     * Load user skills
     */
    protected function loadUserSkills()
    {
        $this->userSkills = $this->user->skills()->get();
    }

    /**
     * Load user certifications
     */
    protected function loadUserCertifications()
    {
        $this->userCertifications = $this->user->certifications()->get();
    }

    /**
     * Calculate campaign eligibility for the user
     */
    protected function calculateCampaignEligibility()
    {
        $campaigns = Campaign::where('status', 'active')->get();

        foreach ($campaigns as $campaign) {
            $isEligible = $this->user->isEligibleFor($campaign);

            // Get missing requirements
            $missingSkills = [];
            $missingCertifications = [];

            foreach ($campaign->requiredSkills as $skill) {
                if ($skill->pivot->is_mandatory &&
                    !$this->user->hasSkill($skill->id, $skill->pivot->minimum_proficiency_level)) {
                    $missingSkills[] = $skill->name . ' (Level ' . $skill->pivot->minimum_proficiency_level . '+)';
                }
            }

            foreach ($campaign->requiredCertifications as $cert) {
                if ($cert->pivot->is_mandatory && !$this->user->hasCertification($cert->id)) {
                    $missingCertifications[] = $cert->name;
                }
            }

            $this->campaignEligibility[] = [
                'id' => $campaign->id,
                'name' => $campaign->name,
                'is_eligible' => $isEligible,
                'missing_skills' => $missingSkills,
                'missing_certifications' => $missingCertifications
            ];
        }
    }

    /**
     * Toggle document verification form
     */
    public function toggleVerificationForm($documentId)
    {
        // Only allow verification by authorized roles
        $authorizedRoles = [1, 2, 3, 12]; // Admin, Director, Site Manager, HR Manager
        if (!in_array(Auth::user()->role_id, $authorizedRoles)) {
            session()->flash('error', 'You are not authorized to verify documents.');
            return;
        }

        $this->selectedDocumentId = $documentId;

        // Find the document to get its current status
        $document = Media::find($documentId);
        if (!$document) {
            session()->flash('error', 'Document not found.');
            return;
        }

        // Toggle editing state
        if (isset($this->documentVerification[$documentId])) {
            $this->documentVerification[$documentId]['editing'] = !$this->documentVerification[$documentId]['editing'];

            // Reset the status to the document's current status when opening the form
            if ($this->documentVerification[$documentId]['editing']) {
                $this->documentVerification[$documentId]['status'] = $document->verification_status ?? 'verified';
                $this->documentVerification[$documentId]['reason'] = $document->rejection_reason ?? '';
            }
        } else {
            $this->documentVerification[$documentId] = [
                'status' => $document->verification_status ?? 'verified',
                'reason' => $document->rejection_reason ?? '',
                'editing' => true
            ];
        }
    }

    /**
     * Update verification status
     */
    public function updateVerificationStatus($documentId, $status)
    {
        if (isset($this->documentVerification[$documentId])) {
            $this->documentVerification[$documentId]['status'] = $status;
        }
    }

    /**
     * Update rejection reason
     */
    public function updateRejectionReason($documentId, $reason)
    {
        if (isset($this->documentVerification[$documentId])) {
            $this->documentVerification[$documentId]['reason'] = $reason;
        }
    }

    /**
     * Save document verification
     */
    public function saveDocumentVerification($documentId)
    {
        // Only allow verification by authorized roles
        $authorizedRoles = [1, 2, 3, 12]; // Admin, Director, Site Manager, HR Manager
        if (!in_array(Auth::user()->role_id, $authorizedRoles)) {
            session()->flash('error', 'You are not authorized to verify documents.');
            return;
        }

        $document = Media::find($documentId);

        if ($document && isset($this->documentVerification[$documentId])) {
            $verificationData = $this->documentVerification[$documentId];

            // Update document verification status
            $document->verification_status = $verificationData['status'];

            if ($verificationData['status'] === 'rejected' && !empty($verificationData['reason'])) {
                $document->rejection_reason = $verificationData['reason'];
            } else {
                $document->rejection_reason = null;
            }

            $document->verified_by = Auth::id();
            $document->verified_at = now();
            $document->save();

            // Close the verification form
            $this->documentVerification[$documentId]['editing'] = false;

            // Update the document in the userDocuments array without reloading
            foreach (['resume', 'id_card', 'certificate', 'other_document'] as $category) {
                if (isset($this->userDocuments[$category])) {
                    foreach ($this->userDocuments[$category] as $key => $doc) {
                        if ($doc['id'] == $documentId) {
                            $this->userDocuments[$category][$key]['verification_status'] = $document->verification_status;
                            $this->userDocuments[$category][$key]['rejection_reason'] = $document->rejection_reason;
                            break 2; // Break both loops once found
                        }
                    }
                }
            }

            session()->flash('success', 'Document verification status updated successfully.');
        } else {
            session()->flash('error', 'Document not found.');
        }
    }

    /**
     * Cancel document verification
     */
    public function cancelDocumentVerification($documentId)
    {
        if (isset($this->documentVerification[$documentId])) {
            $this->documentVerification[$documentId]['editing'] = false;
        }
    }

    /**
     * Determine if the current section should be visible based on user role
     */
    public function shouldShowSection($section, $userRole)
    {
        // First, determine if we're looking at our own profile
        $isOwnProfile = Auth::id() === $this->user->id;

        $roleSections = [
            // Administrator (ID: 1) - sees everything
            1 => ['profile', 'contact', 'documents', 'skills', 'certifications', 'campaign_eligibility', 'document_status', 'performance'],

            // Director (ID: 2)
            2 => ['profile', 'contact', 'documents', 'skills', 'certifications', 'campaign_eligibility', 'document_status', 'performance'],

            // Site Manager (ID: 3)
            3 => ['profile', 'contact', 'documents', 'skills', 'certifications', 'campaign_eligibility', 'document_status', 'performance'],

            // Platform Manager (ID: 4)
            4 => ['profile', 'contact', 'documents', 'skills', 'certifications', 'campaign_eligibility', 'performance'],

            // Supervisor/Campaign Supervisor (ID: 5)
            5 => ['profile', 'contact', 'skills', 'certifications', 'campaign_eligibility', 'performance'],

            // Quality Control (ID: 6)
            6 => ['profile', 'contact', 'skills', 'certifications', 'performance'],

            // Agent (ID: 7) - can see more of their own profile
            7 => $isOwnProfile
                ? ['profile', 'contact', 'documents', 'skills', 'certifications', 'campaign_eligibility', 'performance']
                : ['profile', 'contact'],

            // IT Manager (ID: 8)
            8 => ['profile', 'contact', 'performance'],

            // IT Support (ID: 9)
            9 => ['profile', 'contact'],

            // Trainer (ID: 10)
            10 => ['profile', 'contact', 'skills', 'certifications', 'performance'],

            // Accountant (ID: 11)
            11 => ['profile', 'contact', 'performance'],

            // HR Manager (ID: 12)
            12 => ['profile', 'contact', 'documents', 'skills', 'certifications', 'document_status', 'performance'],
        ];

        // Special case: If viewing an agent profile (role_id 6)
        if ($this->user->role_id == 6) {
            // Supervisors, Quality Control, and Trainers need to see more details for agents
            if (in_array($userRole, [5, 6, 10])) {
                return in_array($section, ['profile', 'contact', 'skills', 'certifications', 'campaign_eligibility', 'performance']);
            }
        }

        // If role not defined, show only basic info
        if (!isset($roleSections[$userRole])) {
            return in_array($section, ['profile', 'contact']);
        }

        return in_array($section, $roleSections[$userRole]);
    }

    public function render()
    {
        return view('livewire.users.enhanced-user-show');
    }
}
