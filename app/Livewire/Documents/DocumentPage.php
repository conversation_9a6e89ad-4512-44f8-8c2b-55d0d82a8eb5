<?php

namespace App\Livewire\Documents;

use App\Livewire\Global\Page;
use App\Models\Media;
use Carbon\Carbon;

class DocumentPage extends Page
{
    public function mount($component = '')
    {
        parent::mount($component);

        $this->pages = [
            [
                'module_id' => 'document-dashboard-module',
                'title' => 'Dashboard',
                'description' => 'Document Dashboard',
                'route' => 'documents.dashboard',
                'display' => true,
                'authorized_permissions' => ['show_document_dashboard'],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'module_id' => 'document-verification-module',
                'title' => 'Verification',
                'description' => 'Document Verification',
                'route' => 'documents.verification',
                'display' => true,
                'authorized_permissions' => ['verify_document'],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'module_id' => 'document-expiration-module',
                'title' => 'Expiration',
                'description' => 'Document Expiration Tracking',
                'route' => 'documents.expiration',
                'display' => true,
                'authorized_permissions' => ['check_document_expiration'],
                'section_routes' => [],
                'sections' => []
            ]
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function setPageResume($route)
    {
        switch ($route) {
            case 'documents.dashboard':
                $totalDocuments = Media::whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])->count();
                $pendingDocuments = Media::where('verification_status', 'pending')->count();
                $verifiedDocuments = Media::where('verification_status', 'verified')->count();
                $expiredDocuments = Media::whereNotNull('expiry_date')->where('expiry_date', '<', Carbon::today())->count();

                $this->resumeTitle = 'Document Dashboard';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Document Management',
                    'description' => 'Overview of all documents in the system',
                    'metrics' => [
                        ['label' => 'Total Documents', 'value' => $totalDocuments],
                        ['label' => 'Pending Verification', 'value' => $pendingDocuments],
                        ['label' => 'Verified Documents', 'value' => $verifiedDocuments],
                        ['label' => 'Expired Documents', 'value' => $expiredDocuments]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Document Dashboard';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalDocuments;
                $this->current_page_resume['description'] = 'Total Documents';
                break;

            case 'documents.verification':
                $pendingVerification = Media::where('verification_status', 'pending')->count();
                $rejectedDocuments = Media::where('verification_status', 'rejected')->count();

                $this->resumeTitle = 'Document Verification';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Document Verification',
                    'description' => 'Verify and approve submitted documents',
                    'metrics' => [
                        ['label' => 'Pending Verification', 'value' => $pendingVerification],
                        ['label' => 'Rejected Documents', 'value' => $rejectedDocuments],
                        ['label' => 'Verification Rate', 'value' => $pendingVerification > 0 ? round(($rejectedDocuments / $pendingVerification) * 100, 1) : 0, 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Document Verification';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $pendingVerification;
                $this->current_page_resume['description'] = 'Pending Verification';
                break;

            case 'documents.expiration':
                $expiredDocuments = Media::whereNotNull('expiry_date')->where('expiry_date', '<', Carbon::today())->count();
                $expiringSoon = Media::whereNotNull('expiry_date')
                    ->where('expiry_date', '>=', Carbon::today())
                    ->where('expiry_date', '<=', Carbon::today()->addDays(30))
                    ->count();

                $this->resumeTitle = 'Document Expiration';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Document Expiration Tracking',
                    'description' => 'Monitor document expiration dates',
                    'metrics' => [
                        ['label' => 'Expired Documents', 'value' => $expiredDocuments],
                        ['label' => 'Expiring Soon', 'value' => $expiringSoon, 'description' => 'Next 30 days'],
                        ['label' => 'Total with Expiry', 'value' => Media::whereNotNull('expiry_date')->count()]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Document Expiration';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $expiredDocuments;
                $this->current_page_resume['description'] = 'Expired Documents';
                break;

            default:
                $totalDocuments = Media::whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])->count();

                $this->resumeTitle = 'Document Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Document Management',
                    'description' => 'Manage all documents in the system',
                    'metrics' => [
                        ['label' => 'Total Documents', 'value' => $totalDocuments],
                        ['label' => 'Active Users', 'value' => \App\Models\User::count()],
                        ['label' => 'Document Categories', 'value' => 4]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Document Management';
                $this->current_page_resume['type'] = 'chart';
                $this->current_page_resume['value'] = $totalDocuments;
                $this->current_page_resume['description'] = 'Total Documents';
                break;
        }
    }

    public function render()
    {
        return view('livewire.documents.document-page');
    }
}
