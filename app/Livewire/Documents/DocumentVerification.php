<?php

namespace App\Livewire\Documents;

use App\Models\Media;
use App\Services\DocumentService;
use Livewire\Component;
use Livewire\WithPagination;

class DocumentVerification extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedCategory = '';
    public $selectedUser = '';
    
    // Verification properties
    public $showVerificationModal = false;
    public $documentId;
    public $verificationStatus = 'verified';
    public $rejectionReason = '';
    
    protected $documentService;
    
    public function boot(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingSelectedCategory()
    {
        $this->resetPage();
    }
    
    public function updatingSelectedUser()
    {
        $this->resetPage();
    }
    
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }
    
    public function openVerificationModal($documentId)
    {
        $this->documentId = $documentId;
        $this->verificationStatus = 'verified';
        $this->rejectionReason = '';
        $this->showVerificationModal = true;
    }
    
    public function closeVerificationModal()
    {
        $this->showVerificationModal = false;
    }
    
    public function verifyDocument()
    {
        $this->validate([
            'verificationStatus' => 'required|in:verified,rejected',
            'rejectionReason' => 'required_if:verificationStatus,rejected',
        ]);
        
        $document = Media::findOrFail($this->documentId);
        
        $this->documentService->verifyDocument(
            $document,
            $this->verificationStatus,
            $this->rejectionReason
        );
        
        $this->closeVerificationModal();
        session()->flash('message', 'Document verification status updated successfully.');
    }
    
    public function render()
    {
        $documents = Media::query()
            ->where('verification_status', 'pending')
            ->when($this->search, function ($query) {
                $query->where('file_name', 'like', '%' . $this->search . '%')
                    ->orWhere('document_title', 'like', '%' . $this->search . '%');
            })
            ->when($this->selectedCategory, function ($query) {
                $query->where('category', $this->selectedCategory);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('mediable_id', $this->selectedUser)
                    ->where('mediable_type', 'App\\Models\\User');
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
        
        return view('livewire.documents.document-verification', [
            'documents' => $documents,
        ]);
    }
}
