<?php

namespace App\Livewire\Global;

use App\Models\Appointment;
use App\Models\Call;
use App\Models\Campaign;
use App\Models\Report;
use App\Models\User;
use App\Models\Site;
use App\Traits\HasPermissionChecks;
use Livewire\Component;

class Sidebar extends Component
{
    use HasPermissionChecks;

    public $modules;

    public function mount()
    {
        $this->modules = $this->filterMenuItems(config('modules'));
    }

    public function getModuleBadge($module)
    {
        switch ($module['id']) {
            case 'user_module':
                $badge = $module['badge'] = User::count();
                break;
            case 'campaign_module':
                $badge = $module['badge'] = Campaign::count();
                break;
            case 'agent_module':
                $badge = $module['badge'] = User::where('role_id', 6)->count();
                break;
            case 'appointment_module':
                $badge = $module['badge'] = Appointment::count();
                break;
            case 'report_module':
                $badge = $module['badge'] = Report::count();
                break;
            case 'site_management_module':
                $badge = $module['badge'] = Site::count();
                break;
            case 'document_management_module':
                $badge = $module['badge'] = \App\Models\Media::where('verification_status', 'pending')->count();
                break;
            case 'call_quality_module':
                $badge = $module['badge'] = \App\Models\Call::whereDoesntHave('evaluations')->count();
                break;
            default:
                $badge = $module['badge'] = '';
                break;
        }

        return $badge;
    }

    public function render()
    {
        return view('livewire.global.sidebar');
    }
}
