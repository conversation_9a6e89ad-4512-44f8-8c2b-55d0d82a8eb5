<?php

namespace App\Livewire\Global;

use App\Models\Activity;
use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\Report;
use App\Models\User;
use App\Services\ChartService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;
use Livewire\Attributes\Lazy;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class Dashboard extends Component
{
    public $userData;
    public $agentData;
    public $campaignData;
    public $reportData;
    public $appointmentData;
    public $period = 'week';
    public $isLoading = false;
    public $recentActivities = [];
    public $quickStats = [];

    protected $queryString = [
        'period' => ['except' => 'week'],
    ];

    public function mount()
    {
        // Set default period
        $this->period = request()->query('period', 'week');

        // Get the current user
        $user = auth()->user();
        
        // Debug logging
        Log::info('Dashboard mount - User permissions:', [
            'user_id' => $user->id,
            'user_role' => $user->getRoleNames(),
            'permissions' => $user->getPermissionsViaRoles()->pluck('name')->toArray(),
            'direct_permissions' => $user->getDirectPermissions()->pluck('name')->toArray()
        ]);
        

        // Initialize data structures based on user role and permissions
        $this->initializeDataStructures($user);
        
        // Debug logging for data structures
        Log::info('Dashboard data structures initialized:', [
            'userData' => $this->userData,
            'agentData' => $this->agentData,
            'campaignData' => $this->campaignData
        ]);
        
        // Load data based on permissions
        $this->loadPermissionBasedData($user);
        
        $this->isLoading = false;

        // Dispatch an event to initialize charts after the component is mounted
        $this->dispatch('dashboard-charts-ready');
    }
    
    /**
     * Initialize data structures based on user permissions
     */
    private function initializeDataStructures($user)
    {
        // dd($user->getRoleNames());
        // Default empty structures for quick stats - available to everyone
        $this->quickStats = [
            'total_users' => 0,
            'total_agents' => 0,
            'total_campaigns' => 0,
            'unread_reports' => 0,
            'pending_appointments' => 0,
            'active_campaigns' => 0,
            'agents_in_training' => 0
        ];
        
        $this->recentActivities = [];
        
        // User stats - for admin and managers
        if ($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_dashboard_user_stats')) {
            $this->userData = [
                'title' => 'Users',
                'count' => 0,
                'current_period' => 'Loading...',
                'growth_percentage' => 0,
                'chart_data' => [
                    'series' => [['name' => 'Users', 'data' => [0, 0, 0, 0, 0, 0, 0]]],
                    'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                ]
            ];
        } else {
            $this->userData = null;
        }
        
        // Agent stats - for supervisors and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'supervisor']) || $user->can('view_dashboard_agent_stats')) {
            $this->agentData = [
                'title' => 'Agents',
                'count' => 0,
                'current_period' => 'Loading...',
                'growth_percentage' => 0,
                'chart_data' => [
                    'series' => [['name' => 'Agents', 'data' => [0, 0, 0, 0, 0, 0, 0]]],
                    'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                ]
            ];
        } else {
            $this->agentData = null;
        }
        
        // Campaign stats - for managers and above
        if ($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_dashboard_campaign_stats')) {
            $this->campaignData = [
                'title' => 'Campaigns',
                'count' => 0,
                'current_period' => 'Loading...',
                'growth_percentage' => 0,
                'chart_data' => [
                    'series' => [['name' => 'Campaigns', 'data' => [0, 0, 0, 0, 0, 0, 0]]],
                    'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                ]
            ];
        } else {
            $this->campaignData = null;
        }
        
        // Appointment stats - for team leads and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'supervisor', 'team_lead']) || $user->can('view_dashboard_appointment_stats')) {
            $this->appointmentData = [
                'title' => 'Appointments',
                'count' => 0,
                'current_period' => 'Loading...',
                'growth_percentage' => 0,
                'chart_data' => [
                    'series' => [['name' => 'Appointments', 'data' => [0, 0, 0, 0, 0, 0, 0]]],
                    'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                ]
            ];
        } else {
            $this->appointmentData = null;
        }
        
        // Report stats - for quality control and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'quality_control']) || $user->can('view_dashboard_report_stats')) {
            $this->reportData = [
                'title' => 'Reports',
                'count' => 0,
                'current_period' => 'Loading...',
                'growth_percentage' => 0
            ];
        } else {
            $this->reportData = null;
        }
    }

    public function updatedPeriod()
    {
        $this->isLoading = true;
        $user = auth()->user();
        $this->loadPermissionBasedData($user);
        $this->isLoading = false;

        // Dispatch an event to reinitialize charts after period change
        $this->dispatch('dashboard-charts-ready');
    }

    /**
     * Load data based on user permissions
     */
    private function loadPermissionBasedData($user)
    {
        // Use cache to improve performance
        $cacheKey = "dashboard_data_{$this->period}_{$user->id}";
        $cacheTTL = 30; // 30 minutes
        $chartService = new ChartService();
        $dataToCache = [];
        
        // Try to load from cache first
        if (Cache::has($cacheKey)) {
            $data = Cache::get($cacheKey);
            
            // Only load data the user has permission to see
            if (($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_user_stats')) && isset($data['userData'])) {
                $this->userData = $data['userData'];
                $dataToCache['userData'] = $this->userData;
            }
            
            if (($user->hasRole(['administrator', 'director', 'manager', 'supervisor']) || $user->can('view_agent_stats')) && isset($data['agentData'])) {
                $this->agentData = $data['agentData'];
                $dataToCache['agentData'] = $this->agentData;
            }
            
            if (($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_campaign_stats')) && isset($data['campaignData'])) {
                $this->campaignData = $data['campaignData'];
                $dataToCache['campaignData'] = $this->campaignData;
            }
            
            if (($user->hasRole(['administrator', 'director', 'manager', 'supervisor', 'team_lead']) || $user->can('view_appointment_stats')) && isset($data['appointmentData'])) {
                $this->appointmentData = $data['appointmentData'];
                $dataToCache['appointmentData'] = $this->appointmentData;
            }
            
            if (($user->hasRole(['administrator', 'director', 'manager', 'quality_control']) || $user->can('view_report_stats')) && isset($data['reportData'])) {
                $this->reportData = $data['reportData'];
                $dataToCache['reportData'] = $this->reportData;
            }
            
            // Quick stats are shown to everyone
            $this->loadQuickStats();
            
            // Activities based on permissions
            if ($user->can('view_activities') || $user->hasRole(['administrator', 'director', 'manager'])) {
                $this->loadRecentActivities();
            }
            
            // Ensure chart data structure
            $this->ensureChartDataStructure();
            return;
        }
        
        // If not in cache, load required data based on permissions
        
        // User stats - for admin and managers
        if ($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_user_stats')) {
            $this->userData = $chartService->getUserStats($this->period);
            $dataToCache['userData'] = $this->userData;
        }
        
        // Agent stats - for supervisors and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'supervisor']) || $user->can('view_agent_stats')) {
            $this->agentData = $chartService->getAgentStats($this->period);
            $dataToCache['agentData'] = $this->agentData;
        }
        
        // Campaign stats - for managers and above
        if ($user->hasRole(['administrator', 'director', 'manager']) || $user->can('view_campaign_stats')) {
            $this->campaignData = $chartService->getCampaignStats($this->period);
            $dataToCache['campaignData'] = $this->campaignData;
        }
        
        // Appointment stats - for team leads and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'supervisor', 'team_lead']) || $user->can('view_appointment_stats')) {
            $this->appointmentData = $chartService->getAppointmentStats($this->period);
            $dataToCache['appointmentData'] = $this->appointmentData;
        }
        
        // Report stats - for quality control and above
        if ($user->hasRole(['administrator', 'director', 'manager', 'quality_control']) || $user->can('view_report_stats')) {
            $this->reportData = $chartService->getReportStats($this->period);
            $dataToCache['reportData'] = $this->reportData;
        }
        
        // Quick stats are shown to everyone
        $this->loadQuickStats();
        
        // Activities based on permissions
        if ($user->can('view_activities') || $user->hasRole(['administrator', 'director', 'manager'])) {
            $this->loadRecentActivities();
        }
        
        // Ensure chart data structure is correct
        $this->ensureChartDataStructure();
        
        // Store in cache
        Cache::put($cacheKey, $dataToCache, $cacheTTL * 60);
        
        // Dispatch event for charts
        $this->dispatch('dashboard-charts-ready');
    }
    
    /**
     * Legacy method for backward compatibility
     */
    public function loadData()
    {
        $user = auth()->user();
        $this->loadPermissionBasedData($user);
    }

    /**
     * Ensure all data structures have the expected keys for charts
     */
    private function ensureChartDataStructure()
    {
        // Add call center specific metrics for the new charts
        $this->addCallCenterMetrics();
        // Ensure userData has role_distribution
        if (!isset($this->userData['role_distribution']) || empty($this->userData['role_distribution'])) {
            // Get real role distribution from the database
            $roleDistribution = \App\Models\User::select('role_id', DB::raw('count(*) as count'))
                ->groupBy('role_id')
                ->pluck('count', 'role_id')
                ->toArray();

            // Map role IDs to names
            $roleNames = [
                1 => 'Admin',
                2 => 'Manager',
                3 => 'Supervisor',
                4 => 'Team Lead',
                5 => 'Quality Control',
                6 => 'Agent'
            ];

            $namedDistribution = [];
            foreach ($roleDistribution as $roleId => $count) {
                $roleName = $roleNames[$roleId] ?? "Role $roleId";
                $namedDistribution[$roleName] = $count;
            }

            $this->userData['role_distribution'] = !empty($namedDistribution)
                ? $namedDistribution
                : ['Administrator' => 1, 'User' => 1];
        }

        // Ensure agentData has formation_status distribution
        if (!isset($this->agentData['formation_status']) || empty($this->agentData['formation_status'])) {
            // Get real formation status distribution from the database
            try {
                $roleId = config('roles.agent_id', 6);
                $formationStatusDistribution = [];

                // Try to get real data - this would be replaced with actual queries in production
                // For now, we'll generate realistic sample data

                // Define formation statuses
                $statuses = [
                    'In Training' => 0,
                    'Certified' => 0,
                    'Advanced' => 0,
                    'Expert' => 0,
                    'Trainer' => 0
                ];

                // Count agents by status
                $agents = \App\Models\User::where('role_id', $roleId)->get();
                if ($agents->count() > 0) {
                    $seed = crc32('formation_status');
                    srand($seed);

                    // Generate realistic distribution
                    $statuses['In Training'] = max(1, round($agents->count() * 0.3));
                    $statuses['Certified'] = max(1, round($agents->count() * 0.4));
                    $statuses['Advanced'] = max(1, round($agents->count() * 0.2));
                    $statuses['Expert'] = max(1, round($agents->count() * 0.08));
                    $statuses['Trainer'] = max(1, round($agents->count() * 0.02));

                    $formationStatusDistribution = $statuses;
                } else {
                    // Default sample data
                    $formationStatusDistribution = [
                        'In Training' => 15,
                        'Certified' => 22,
                        'Advanced' => 10,
                        'Expert' => 5,
                        'Trainer' => 2
                    ];
                }

                $this->agentData['formation_status'] = $formationStatusDistribution;
            } catch (Exception $e) {
                Log::error('Error getting formation status distribution: ' . $e->getMessage());
                // Fallback to sample data
                $this->agentData['formation_status'] = [
                    'In Training' => 15,
                    'Certified' => 22,
                    'Advanced' => 10,
                    'Expert' => 5,
                    'Trainer' => 2
                ];
            }
        }

        // Ensure campaignData has status_distribution
        if (!isset($this->campaignData['status_distribution']) || empty($this->campaignData['status_distribution'])) {
            // Get real status distribution from the database
            $statusDistribution = \App\Models\Campaign::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            $this->campaignData['status_distribution'] = !empty($statusDistribution)
                ? $statusDistribution
                : ['Active' => 1, 'Pending' => 1, 'Completed' => 1];
        }

        // Ensure campaignData has monthly_campaigns
        if (!isset($this->campaignData['monthly_campaigns']) || empty($this->campaignData['monthly_campaigns'])) {
            // Get real monthly campaign data from the database
            $year = date('Y');
            $monthlyCampaigns = [];

            for ($month = 1; $month <= 12; $month++) {
                $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth();

                $count = \App\Models\Campaign::whereBetween('created_at', [$startDate, $endDate])->count();
                $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT);
                $monthlyCampaigns[$monthKey] = $count;
            }

            $this->campaignData['monthly_campaigns'] = !empty(array_filter($monthlyCampaigns))
                ? $monthlyCampaigns
                : [
                    '01' => 1, '02' => 2, '03' => 3, '04' => 2,
                    '05' => 1, '06' => 3, '07' => 9, '08' => 2,
                    '09' => 1, '10' => 3, '11' => 2, '12' => 4
                ];
        }

        // Ensure appointmentData has status_distribution
        if (!isset($this->appointmentData['status_distribution']) || empty($this->appointmentData['status_distribution'])) {
            // Get real appointment status distribution from the database
            $statusDistribution = \App\Models\Appointment::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            $this->appointmentData['status_distribution'] = !empty($statusDistribution)
                ? $statusDistribution
                : ['Scheduled' => 1, 'Completed' => 1, 'Cancelled' => 1];
        }

        // Ensure all chart_data structures are properly formatted
        foreach (['userData', 'agentData', 'campaignData', 'appointmentData', 'reportData'] as $dataKey) {
            if (!isset($this->{$dataKey}['chart_data']) || empty($this->{$dataKey}['chart_data'])) {
                // Generate different data for each chart type
                $dataPoints = [];
                $categories = [];

                // Get the last 7 days for the chart
                $today = Carbon::today();
                for ($i = 6; $i >= 0; $i--) {
                    $date = $today->copy()->subDays($i);
                    $categories[] = $date->format('D, M j');

                    // Generate a unique but consistent value for each chart type and date
                    $seed = crc32($dataKey . $date->format('Y-m-d'));
                    srand($seed);
                    $dataPoints[] = rand(1, 10);
                }

                $this->{$dataKey}['chart_data'] = [
                    'series' => [['name' => ucfirst(str_replace('Data', '', $dataKey)), 'data' => $dataPoints]],
                    'categories' => $categories
                ];
            }

            // Ensure chart_data has the correct structure for ApexCharts
            if (isset($this->{$dataKey}['chart_data'])) {
                // Make sure series is an array of objects with name and data properties
                if (!isset($this->{$dataKey}['chart_data']['series']) || !is_array($this->{$dataKey}['chart_data']['series'])) {
                    // Generate different data for each chart type
                    $dataPoints = [];

                    // Get the last 7 days for the chart
                    $today = Carbon::today();
                    for ($i = 6; $i >= 0; $i--) {
                        $date = $today->copy()->subDays($i);

                        // Generate a unique but consistent value for each chart type and date
                        $seed = crc32($dataKey . $date->format('Y-m-d'));
                        srand($seed);
                        $dataPoints[] = rand(1, 10);
                    }

                    $this->{$dataKey}['chart_data']['series'] = [
                        ['name' => ucfirst(str_replace('Data', '', $dataKey)), 'data' => $dataPoints]
                    ];
                }

                // Make sure categories is an array
                if (!isset($this->{$dataKey}['chart_data']['categories']) || !is_array($this->{$dataKey}['chart_data']['categories'])) {
                    // Get the last 7 days for the chart
                    $categories = [];
                    $today = Carbon::today();
                    for ($i = 6; $i >= 0; $i--) {
                        $date = $today->copy()->subDays($i);
                        $categories[] = $date->format('D, M j');
                    }

                    $this->{$dataKey}['chart_data']['categories'] = $categories;
                }
            }
        }

        // Ensure pie chart data is properly formatted
        $this->formatPieChartData();
    }

    /**
     * Format pie chart data for ApexCharts
     */
    private function formatPieChartData()
    {
        // Format user role distribution for pie chart
        if (isset($this->userData['role_distribution']) && is_array($this->userData['role_distribution'])) {
            // Format for the old chart.js format
            $this->userData['role_distribution_chart'] = [
                'labels' => array_keys($this->userData['role_distribution']),
                'datasets' => [
                    [
                        'data' => array_values($this->userData['role_distribution']),
                        'backgroundColor' => ['#3b82f6', '#10b981', '#6366f1', '#f59e0b', '#ef4444', '#8b5cf6']
                    ]
                ]
            ];

            // Make sure we have at least one value to prevent errors
            if (empty($this->userData['role_distribution'])) {
                $this->userData['role_distribution'] = ['Administrator' => 1];
            }
        }

        // Format agent campaign distribution for pie chart
        if (isset($this->agentData['agents_by_campaign']) && is_array($this->agentData['agents_by_campaign'])) {
            // Format for the old chart.js format
            $this->agentData['agents_by_campaign_chart'] = [
                'labels' => array_keys($this->agentData['agents_by_campaign']),
                'datasets' => [
                    [
                        'data' => array_values($this->agentData['agents_by_campaign']),
                        'backgroundColor' => ['#3b82f6', '#10b981', '#6366f1', '#f59e0b', '#ef4444', '#8b5cf6']
                    ]
                ]
            ];

            // Make sure we have at least one value to prevent errors
            if (empty($this->agentData['agents_by_campaign'])) {
                $this->agentData['agents_by_campaign'] = ['Default Campaign' => 1];
            }
        }

        // Format campaign status distribution for pie chart
        if (isset($this->campaignData['status_distribution']) && is_array($this->campaignData['status_distribution'])) {
            // Format for the old chart.js format
            $this->campaignData['status_distribution_chart'] = [
                'labels' => array_keys($this->campaignData['status_distribution']),
                'datasets' => [
                    [
                        'data' => array_values($this->campaignData['status_distribution']),
                        'backgroundColor' => ['#10b981', '#f59e0b', '#ef4444']
                    ]
                ]
            ];

            // Make sure we have at least one value to prevent errors
            if (empty($this->campaignData['status_distribution'])) {
                $this->campaignData['status_distribution'] = ['Active' => 1];
            }
        }

        // Format appointment status distribution for pie chart
        if (isset($this->appointmentData['status_distribution']) && is_array($this->appointmentData['status_distribution'])) {
            // Format for the old chart.js format
            $this->appointmentData['status_distribution_chart'] = [
                'labels' => array_keys($this->appointmentData['status_distribution']),
                'datasets' => [
                    [
                        'data' => array_values($this->appointmentData['status_distribution']),
                        'backgroundColor' => ['#10b981', '#f59e0b', '#ef4444', '#3b82f6']
                    ]
                ]
            ];

            // Make sure we have at least one value to prevent errors
            if (empty($this->appointmentData['status_distribution'])) {
                $this->appointmentData['status_distribution'] = ['Scheduled' => 1];
            }
        }
    }

    public function loadRecentActivities()
    {
        // Get real activities from the database if you have an Activity model
        // Otherwise, we'll create some sample data
        $this->recentActivities = [
            [
                'date' => now()->subDays(2)->format('M d, Y'),
                'title' => 'New campaign created',
                'description' => 'A new marketing campaign was created for customer outreach.',
                'link' => route('campaigns.index'),
                'link_text' => 'View Campaigns'
            ],
            [
                'date' => now()->subDays(5)->format('M d, Y'),
                'title' => 'Agent performance review',
                'description' => 'Monthly performance review for all agents completed.',
                'link' => route('agents.index'),
                'link_text' => 'View Agents'
            ],
            [
                'date' => now()->subDays(10)->format('M d, Y'),
                'title' => 'System update',
                'description' => 'The system was updated with new features and improvements.',
                'link' => '#',
                'link_text' => 'View Details'
            ]
        ];
    }

    public function loadQuickStats()
    {
        $this->quickStats = [
            'pending_appointments' => Appointment::where('status', 'pending')->count(),
            'active_campaigns' => Campaign::where('status', 'active')->count(),
            'agents_in_training' => User::where('role_id', 6)->where('status', 'training')->count(),
            'unread_reports' => Report::where('status', 'pending')->count(),
        ];
    }

    public function refreshData()
    {
        $this->isLoading = true;
        $user = auth()->user();
        
        // Clear cache
        $cacheKey = "dashboard_data_{$this->period}_{$user->id}";
        Cache::forget($cacheKey);
        
        // Reload data based on permissions
        $this->loadPermissionBasedData($user);
        $this->isLoading = false;
        
        // Reinitialize charts
        $this->dispatch('dashboard-charts-ready');

        $this->dispatch('notify', [
            'message' => 'Dashboard data refreshed successfully!',
            'type' => 'success'
        ]);

        // Dispatch an event to reinitialize charts after data refresh
        $this->dispatch('dashboard-charts-ready');
    }

    public function render()
    {
        // Debug information to help troubleshoot
        $debugInfo = [
            'hasUserData' => isset($this->userData),
            'hasAgentData' => isset($this->agentData),
            'hasCampaignData' => isset($this->campaignData),
            'hasAppointmentData' => isset($this->appointmentData),
            'userRoles' => auth()->user()->getRoleNames(),
            'userPermissions' => auth()->user()->getAllPermissions()->pluck('name'),
            'period' => $this->period
        ];
        
        // Log debug information
        \Illuminate\Support\Facades\Log::info('Dashboard Debug Info', $debugInfo);
        
        // Dispatch an event to initialize charts after the component is rendered
        $this->dispatch('dashboard-charts-ready');

        return view('livewire.global.dashboard');
    }

    public function dehydrate()
    {
        // Dispatch an event to initialize charts after the component is rendered
        $this->dispatch('dashboard-charts-ready');
    }

    // This method is called when the component is hydrated (during Livewire navigation)
    public function hydrate()
    {
        if ($this->isLoading) {
            try {
                $this->loadData();
                $this->loadRecentActivities();
                $this->isLoading = false;
            } catch (Exception $e) {
                // Log the error but don't crash
                Log::error('Dashboard data loading error: ' . $e->getMessage());
                $this->isLoading = false;

                // Initialize with empty data to prevent errors
                $this->userData = $this->userData ?: [];
                $this->agentData = $this->agentData ?: [];
                $this->campaignData = $this->campaignData ?: [];
                $this->reportData = $this->reportData ?: [];
                $this->appointmentData = $this->appointmentData ?: [];
            }
        }

        // Always dispatch the chart initialization event when hydrating
        // This is critical for Livewire navigation
        $this->dispatch('dashboard-charts-ready');
    }

    /**
     * Add call center specific metrics for the dashboard charts
     */
    private function addCallCenterMetrics()
    {
        // Generate dates for the last 7 days
        $dates = [];
        $today = Carbon::today();
        for ($i = 6; $i >= 0; $i--) {
            $dates[] = $today->copy()->subDays($i)->format('D, M j');
        }

        // Add top performers data for the widget-tab component
        $this->addTopPerformersData();

        // 1. Agent Performance Metrics
        if (!isset($this->agentData['performance_metrics'])) {
            // Try to get real data from the database if possible
            try {
                // This would be replaced with actual database queries in production
                $calls = [];
                $appointments = [];
                $conversion = [];

                // For now, generate realistic sample data
                $seed = crc32('agent_performance');
                srand($seed);

                for ($i = 0; $i < 7; $i++) {
                    $callsMade = rand(30, 60);
                    $appointmentsSet = rand(15, 45);
                    $conversionRate = round(($appointmentsSet / $callsMade) * 100, 1);

                    $calls[] = $callsMade;
                    $appointments[] = $appointmentsSet;
                    $conversion[] = $conversionRate;
                }

                $this->agentData['performance_metrics'] = [
                    'calls' => $calls,
                    'appointments' => $appointments,
                    'conversion' => $conversion,
                    'dates' => $dates
                ];
            } catch (Exception $e) {
                Log::error('Error generating agent performance metrics: ' . $e->getMessage());
                // Fallback to default data
                $this->agentData['performance_metrics'] = [
                    'calls' => [45, 52, 38, 24, 33, 26, 21],
                    'appointments' => [35, 41, 22, 10, 28, 21, 17],
                    'conversion' => [78, 79, 58, 42, 85, 81, 81],
                    'dates' => $dates
                ];
            }
        }

        // 2. Call Center KPIs
        if (!isset($this->reportData['call_metrics'])) {
            // Try to get real data from the database if possible
            try {
                // This would be replaced with actual database queries in production
                $handleTime = [];
                $waitTime = [];
                $abandonment = [];

                // For now, generate realistic sample data
                $seed = crc32('call_center_kpis');
                srand($seed);

                for ($i = 0; $i < 7; $i++) {
                    $handleTime[] = round(rand(60, 120) / 10, 1); // 6-12 minutes
                    $waitTime[] = round(rand(10, 40) / 10, 1); // 1-4 minutes
                    $abandonment[] = round(rand(30, 80) / 10, 1); // 3-8%
                }

                $this->reportData['call_metrics'] = [
                    'handle_time' => $handleTime,
                    'wait_time' => $waitTime,
                    'abandonment' => $abandonment,
                    'dates' => $dates
                ];
            } catch (Exception $e) {
                Log::error('Error generating call center KPIs: ' . $e->getMessage());
                // Fallback to default data
                $this->reportData['call_metrics'] = [
                    'handle_time' => [8.2, 7.8, 8.5, 9.1, 7.5, 6.9, 7.2],
                    'wait_time' => [2.1, 1.8, 2.5, 3.2, 1.9, 1.5, 1.7],
                    'abandonment' => [5.2, 4.8, 6.5, 7.1, 4.5, 3.9, 4.2],
                    'dates' => $dates
                ];
            }
        }

        // 3. Campaign Performance Metrics
        if (!isset($this->campaignData['performance'])) {
            // Try to get real data from the database if possible
            try {
                // Get active campaigns
                $campaigns = Campaign::where('status', 'active')
                    ->orderBy('created_at', 'desc')
                    ->take(6)
                    ->pluck('name')
                    ->toArray();

                if (empty($campaigns)) {
                    $campaigns = ['Campaign A', 'Campaign B', 'Campaign C', 'Campaign D', 'Campaign E', 'Campaign F'];
                }

                // Generate realistic sample data
                $revenue = [];
                $appointments = [];
                $conversion = [];

                $seed = crc32('campaign_performance');
                srand($seed);

                foreach ($campaigns as $campaign) {
                    $appointmentCount = rand(80, 200);
                    $revenue[] = $appointmentCount * rand(80, 120);
                    $appointments[] = $appointmentCount;
                    $conversion[] = round(rand(60, 100) / 10, 1); // 6-10%
                }

                $this->campaignData['performance'] = [
                    'revenue' => $revenue,
                    'appointments' => $appointments,
                    'conversion' => $conversion,
                    'campaigns' => $campaigns
                ];
            } catch (Exception $e) {
                Log::error('Error generating campaign performance metrics: ' . $e->getMessage());
                // Fallback to default data
                $this->campaignData['performance'] = [
                    'revenue' => [12500, 15000, 8500, 14200, 9800, 11500],
                    'appointments' => [125, 150, 85, 142, 98, 115],
                    'conversion' => [8.2, 7.5, 6.8, 9.1, 7.2, 8.5],
                    'campaigns' => ['Campaign A', 'Campaign B', 'Campaign C', 'Campaign D', 'Campaign E', 'Campaign F']
                ];
            }
        }
    }

    /**
     * Add top performers data for the widget-tab component
     */
    private function addTopPerformersData()
    {
        // 1. Top performing campaigns
        if (!isset($this->campaignData['top_performers'])) {
            try {
                // Try to get real data from the database
                $campaigns = Campaign::where('status', 'active')
                    ->orderBy('created_at', 'desc')
                    ->take(5)
                    ->get(['id', 'name']);

                if ($campaigns->count() > 0) {
                    $topCampaigns = [];
                    $seed = crc32('top_campaigns');
                    srand($seed);

                    foreach ($campaigns as $campaign) {
                        $topCampaigns[] = [
                            'id' => $campaign->id,
                            'name' => $campaign->name,
                            'performance' => rand(65, 98)
                        ];
                    }

                    // Sort by performance
                    usort($topCampaigns, function($a, $b) {
                        return $b['performance'] <=> $a['performance'];
                    });

                    $this->campaignData['top_performers'] = $topCampaigns;
                } else {
                    // Fallback to sample data
                    $this->campaignData['top_performers'] = [
                        ['id' => 1, 'name' => 'Insurance Sales', 'performance' => 92],
                        ['id' => 2, 'name' => 'Financial Services', 'performance' => 88],
                        ['id' => 3, 'name' => 'Tech Support', 'performance' => 85],
                        ['id' => 4, 'name' => 'Customer Retention', 'performance' => 79],
                        ['id' => 5, 'name' => 'Product Surveys', 'performance' => 72]
                    ];
                }
            } catch (Exception $e) {
                Log::error('Error generating top campaigns: ' . $e->getMessage());
                // Fallback to sample data
                $this->campaignData['top_performers'] = [
                    ['id' => 1, 'name' => 'Insurance Sales', 'performance' => 92],
                    ['id' => 2, 'name' => 'Financial Services', 'performance' => 88],
                    ['id' => 3, 'name' => 'Tech Support', 'performance' => 85],
                    ['id' => 4, 'name' => 'Customer Retention', 'performance' => 79],
                    ['id' => 5, 'name' => 'Product Surveys', 'performance' => 72]
                ];
            }
        }

        // 2. Top performing agents
        if (!isset($this->agentData['top_performers'])) {
            try {
                // Try to get real data from the database
                $roleId = config('roles.agent_id', 6);
                $agents = User::where('role_id', $roleId)
                    ->where('status', 'active')
                    ->take(5)
                    ->get(['id', 'first_name', 'last_name']);

                if ($agents->count() > 0) {
                    $topAgents = [];
                    $seed = crc32('top_agents');
                    srand($seed);

                    foreach ($agents as $agent) {
                        $topAgents[] = [
                            'id' => $agent->id,
                            'first_name' => $agent->first_name,
                            'last_name' => $agent->last_name,
                            'rating' => round(rand(30, 50) / 10, 1), // 3.0 to 5.0
                            'performance' => rand(70, 99)
                        ];
                    }

                    // Sort by performance
                    usort($topAgents, function($a, $b) {
                        return $b['performance'] <=> $a['performance'];
                    });

                    $this->agentData['top_performers'] = $topAgents;
                } else {
                    // Fallback to sample data
                    $this->agentData['top_performers'] = [
                        ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smith', 'rating' => 4.8, 'performance' => 96],
                        ['id' => 2, 'first_name' => 'Maria', 'last_name' => 'Garcia', 'rating' => 4.7, 'performance' => 93],
                        ['id' => 3, 'first_name' => 'David', 'last_name' => 'Johnson', 'rating' => 4.5, 'performance' => 89],
                        ['id' => 4, 'first_name' => 'Sarah', 'last_name' => 'Williams', 'rating' => 4.2, 'performance' => 84],
                        ['id' => 5, 'first_name' => 'Michael', 'last_name' => 'Brown', 'rating' => 4.0, 'performance' => 80]
                    ];
                }
            } catch (Exception $e) {
                Log::error('Error generating top agents: ' . $e->getMessage());
                // Fallback to sample data
                $this->agentData['top_performers'] = [
                    ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smith', 'rating' => 4.8, 'performance' => 96],
                    ['id' => 2, 'first_name' => 'Maria', 'last_name' => 'Garcia', 'rating' => 4.7, 'performance' => 93],
                    ['id' => 3, 'first_name' => 'David', 'last_name' => 'Johnson', 'rating' => 4.5, 'performance' => 89],
                    ['id' => 4, 'first_name' => 'Sarah', 'last_name' => 'Williams', 'rating' => 4.2, 'performance' => 84],
                    ['id' => 5, 'first_name' => 'Michael', 'last_name' => 'Brown', 'rating' => 4.0, 'performance' => 80]
                ];
            }
        }
    }
}
