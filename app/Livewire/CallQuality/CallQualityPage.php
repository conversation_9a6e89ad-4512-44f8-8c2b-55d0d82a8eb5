<?php

namespace App\Livewire\CallQuality;

use App\Livewire\Global\Page;

class CallQualityPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = [];
    public array $current_page_section = [];

    public function mount($component = '')
    {
        parent::mount($component);

        // Get call from route if available
        $call = request()->route('call');
        $this->pages = [
            [
                'name' => 'Dashboard',
                'icon' => 'dashboard',
                'route' => 'call-quality.dashboard',
                'component' => 'call-quality.dashboard',
            ],
            [
                'name' => 'Calls',
                'icon' => 'phone',
                'route' => 'call-quality.calls',
                'component' => 'call-quality.call-index',
            ],
            [
                'name' => 'Evaluations',
                'icon' => 'clipboard-check',
                'route' => 'call-quality.evaluations',
                'component' => 'call-quality.evaluation-index',
            ],
            [
                'name' => 'Criteria',
                'icon' => 'clipboard-list',
                'route' => 'call-quality.criteria',
                'component' => 'call-quality.criteria-index',
            ],
            [
                'name' => 'Reports',
                'icon' => 'chart-bar',
                'route' => 'call-quality.reports',
                'component' => 'call-quality.quality-reports',
            ],
            [
                'name' => 'Settings',
                'icon' => 'cog',
                'route' => 'call-quality.settings',
                'component' => 'call-quality.quality-settings',
            ],
        ];

        $this->current_page = $this->pages[0];

        if ($component) {
            foreach ($this->pages as $page) {
                if ($page['component'] === $component) {
                    $this->current_page = $page;
                    break;
                }
            }
        }

        $this->current_page_resume = [
            'title' => $this->current_page['name'],
            'description' => 'Manage call quality monitoring and evaluations',
            'icon' => $this->current_page['icon'],
        ];

        $this->current_page_section = [
            'title' => 'Call Quality',
            'icon' => 'phone',
        ];
    }

    public function render()
    {
        return view('livewire.call-quality.call-quality-page');
    }
}
