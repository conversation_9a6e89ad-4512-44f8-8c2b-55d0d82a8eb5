<?php

namespace App\Livewire\Forms;

use App\Models\Campaign;
use App\Models\Department;
use App\Models\Media;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Form;

class UserForm extends Form
{
    public $first_name = '';
    public $last_name = '';
    public $country = '';
    public $city = '';
    public $address = '';
    public $email = '';
    public $phone_number = '';
    public $birth_date = '';
    public $role_id = '';
    public $campaign_id = '';
    public $department_id = '';
    public $manager_id = '';
    public $job_title = '';
    public $employment_type = '';
    public $hierarchy_level = '';
    public $registration_number = '';
    public $hire_date = '';
    public $status = 'in_training';
    public $password = '';
    public $password_confirmation = '';
    public $new_password = '';
    public $profile_picture;
    public $current_profile_picture;
    public $canDisplayCampaign = false;

    // Document uploads
    public $document_department = 'HR'; // Default department for documents

    public $resume;
    public $resume_title = '';
    public $resume_description = '';
    public $resume_expiry_date = '';

    public $id_card;
    public $id_card_title = '';
    public $id_card_description = '';
    public $id_card_expiry_date = '';

    public $certificates = [];
    public $certificate_titles = [];
    public $certificate_descriptions = [];
    public $certificate_expiry_dates = [];

    public $other_documents = [];
    public $other_document_titles = [];
    public $other_document_descriptions = [];
    public $other_document_expiry_dates = [];

    public $current_documents = [];

    protected $rules = [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'birth_date' => 'nullable|date|before:today',
        'email' => 'required|email|max:255',
        'phone_number' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:255',
        'city' => 'nullable|string|max:100',
        'country' => 'nullable|string|max:100',
        'password' => 'required|string|min:8|confirmed',
        'password_confirmation' => 'required_with:password|string|min:8',
        'role_id' => 'required|exists:roles,id',
        'status' => 'required|in:in_training,actif,inactif,active,inactive,engaged',
        'hire_date' => 'nullable|date|before_or_equal:today',
        'department_id' => 'nullable|exists:departments,id',
        'manager_id' => 'nullable|exists:users,id',
        'job_title' => 'nullable|string|max:100',
        'employment_type' => 'nullable|string|max:50',
        'hierarchy_level' => 'nullable|integer|min:1|max:10',
        'profile_picture' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',

        // Document validation
        'document_department' => 'nullable|string|max:100',
        'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        'resume_title' => 'nullable|string|max:255',
        'resume_description' => 'nullable|string|max:1000',
        'resume_expiry_date' => 'nullable|date',

        'id_card' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        'id_card_title' => 'nullable|string|max:255',
        'id_card_description' => 'nullable|string|max:1000',
        'id_card_expiry_date' => 'nullable|date',

        'certificates.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
        'certificate_titles.*' => 'nullable|string|max:255',
        'certificate_descriptions.*' => 'nullable|string|max:1000',
        'certificate_expiry_dates.*' => 'nullable|date',

        'other_documents.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:10240',
        'other_document_titles.*' => 'nullable|string|max:255',
        'other_document_descriptions.*' => 'nullable|string|max:1000',
        'other_document_expiry_dates.*' => 'nullable|date',
    ];

    protected $messages = [
        'first_name.required' => 'First name is required',
        'last_name.required' => 'Last name is required',
        'email.required' => 'Email address is required',
        'email.email' => 'Please enter a valid email address',
        'email.unique' => 'This email is already in use',
        'password.required' => 'Password is required',
        'password.min' => 'Password must be at least 8 characters',
        'password.confirmed' => 'Password confirmation does not match',
        'role_id.required' => 'Please select a role',
        'role_id.exists' => 'Selected role is invalid',
        'campaign_id.required' => 'Please select a campaign for this manager',
        'profile_picture.mimes' => 'Profile picture must be a JPG, JPEG, or PNG file',
        'profile_picture.max' => 'Profile picture must not exceed 2MB',

        // Document validation messages
        'document_department.max' => 'Department name must not exceed 100 characters',
        'resume.mimes' => 'Resume must be a PDF, DOC, or DOCX file',
        'resume.max' => 'Resume must not exceed 5MB',
        'resume_title.max' => 'Resume title must not exceed 255 characters',
        'resume_description.max' => 'Resume description must not exceed 1000 characters',
        'resume_expiry_date.date' => 'Resume expiry date must be a valid date',

        'id_card.mimes' => 'ID card must be a PDF, JPG, JPEG, or PNG file',
        'id_card.max' => 'ID card must not exceed 2MB',
        'id_card_title.max' => 'ID card title must not exceed 255 characters',
        'id_card_description.max' => 'ID card description must not exceed 1000 characters',
        'id_card_expiry_date.date' => 'ID card expiry date must be a valid date',

        'certificates.*.mimes' => 'Certificates must be PDF, JPG, JPEG, or PNG files',
        'certificates.*.max' => 'Each certificate must not exceed 5MB',
        'certificate_titles.*.max' => 'Certificate title must not exceed 255 characters',
        'certificate_descriptions.*.max' => 'Certificate description must not exceed 1000 characters',
        'certificate_expiry_dates.*.date' => 'Certificate expiry date must be a valid date',

        'other_documents.*.mimes' => 'Documents must be PDF, DOC, DOCX, JPG, JPEG, PNG, XLS, or XLSX files',
        'other_documents.*.max' => 'Each document must not exceed 10MB',
        'other_document_titles.*.max' => 'Document title must not exceed 255 characters',
        'other_document_descriptions.*.max' => 'Document description must not exceed 1000 characters',
        'other_document_expiry_dates.*.date' => 'Document expiry date must be a valid date',
    ];


    public function setUser(User $user)
    {
        // Set basic user data
        $this->first_name = $user->first_name;
        $this->last_name = $user->last_name;
        $this->birth_date = $user->birth_date;
        $this->country = $user->country;
        $this->city = $user->city;
        $this->address = $user->address;
        $this->email = $user->email;
        $this->phone_number = $user->phone_number;
        $this->role_id = $user->role_id;
        $this->campaign_id = $user->campaign_id;
        $this->department_id = $user->department_id;
        $this->manager_id = $user->manager_id;
        $this->job_title = $user->job_title;
        $this->employment_type = $user->employment_type;
        $this->hierarchy_level = $user->hierarchy_level;
        $this->hire_date = $user->hire_date;
        $this->status = $user->status;

        // Set current profile picture URL
        $this->current_profile_picture = $user->getProfilePictureUrl();

        // Load existing documents
        $this->current_documents = $user->media()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->get()
            ->groupBy('category')
            ->toArray();

        // Load document metadata for existing documents
        $resume = $user->media()->where('category', 'resume')->first();
        if ($resume) {
            $this->resume_title = $resume->document_title;
            $this->resume_description = $resume->description;
            $this->resume_expiry_date = $resume->expiry_date ? $resume->expiry_date->format('Y-m-d') : null;
        }

        $idCard = $user->media()->where('category', 'id_card')->first();
        if ($idCard) {
            $this->id_card_title = $idCard->document_title;
            $this->id_card_description = $idCard->description;
            $this->id_card_expiry_date = $idCard->expiry_date ? $idCard->expiry_date->format('Y-m-d') : null;
        }
    }

    /**
     * Validate all form fields
     *
     * @return array Validated data
     * @throws \Illuminate\Validation\ValidationException
     */
    public function validateAllFields()
    {
        // Set campaign_id validation rule based on role
        $rules = $this->rules;

        // Only require campaign_id if the role is manager
        if ($this->canDisplayCampaign) {
            $rules['campaign_id'] = 'required|exists:campaigns,id';
        } else {
            $rules['campaign_id'] = 'nullable|exists:campaigns,id';
        }

        // Validate all fields and let the ValidationException bubble up
        return $this->validate($rules, $this->messages);
    }

    public function store()
    {
        // Validate all fields
        $data = $this->validateAllFields();

        // Check if email is unique
        $existingUser = User::where('email', $data['email'])->first();
        if ($existingUser) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                'email' => ['This email address is already in use.'],
            ]);
        }

        $role = Role::find($data['role_id']);
        if ($role->name !== 'admin') {
            $lastEmp = User::whereHas('role', fn($q) => $q->where('name', '!=', 'admin'))
                ->orderBy('registration_number', 'desc')
                ->first();
            $nextNumber = $lastEmp ? (int) substr($lastEmp->registration_number, 4) + 1 : 1;
            $data['registration_number'] = 'EMP-' . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
        } else {
            $data['registration_number'] = null; // Pas de numéro pour les admins
        }

        // Prepare user data
        $userData = [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'birth_date' => $data['birth_date'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'address' => $data['address'],
            'city' => $data['city'],
            'country' => $data['country'],
            'password' => bcrypt($data['password']),
            'role_id' => $data['role_id'],
            'status' => $data['status'],
            'registration_number' => $data['registration_number'],
            'hire_date' => $data['hire_date'],
        ];

        // Only add campaign_id if it's a valid value
        if ($role->name === 'manager' && !empty($data['campaign_id'])) {
            // Verify the campaign exists
            $campaign = Campaign::find($data['campaign_id']);
            if ($campaign) {
                $userData['campaign_id'] = $data['campaign_id'];
            }
        }

        // Only add department_id if it's a valid value
        if (!empty($data['department_id'])) {
            $department = Department::find($data['department_id']);
            if ($department) {
                $userData['department_id'] = $data['department_id'];
            }
        }

        // Only add manager_id if it's a valid value
        if (!empty($data['manager_id'])) {
            $manager = User::find($data['manager_id']);
            if ($manager) {
                $userData['manager_id'] = $data['manager_id'];
            }
        }

        // Add other optional fields if they have values
        if (!empty($data['job_title'])) {
            $userData['job_title'] = $data['job_title'];
        }

        if (!empty($data['employment_type'])) {
            $userData['employment_type'] = $data['employment_type'];
        }

        if (!empty($data['hierarchy_level'])) {
            $userData['hierarchy_level'] = $data['hierarchy_level'];
        }

        // Create the user with the prepared data
        $user = User::create($userData);

        // Upload profile picture
        if ($this->profile_picture) {
            $filePath = $this->profile_picture->store('media/profile_pictures', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->profile_picture->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->profile_picture->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => Auth::id(),
            ]);
        }

        // Upload resume with metadata
        if ($this->resume) {
            $filePath = $this->resume->store('media/documents/resumes', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->resume->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->resume->getMimeType(),
                'category' => 'resume',
                'uploaded_by' => Auth::id(),
                'document_title' => $this->resume_title ?: $this->resume->getClientOriginalName(),
                'description' => $this->resume_description,
                'expiry_date' => $this->resume_expiry_date,
                'department' => $this->document_department,
                'verification_status' => 'pending',
            ]);
        }

        // Upload ID card with metadata
        if ($this->id_card) {
            $filePath = $this->id_card->store('media/documents/id_cards', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->id_card->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->id_card->getMimeType(),
                'category' => 'id_card',
                'uploaded_by' => Auth::id(),
                'document_title' => $this->id_card_title ?: $this->id_card->getClientOriginalName(),
                'description' => $this->id_card_description,
                'expiry_date' => $this->id_card_expiry_date,
                'department' => $this->document_department,
                'verification_status' => 'pending',
            ]);
        }

        // Upload certificates with metadata
        if (!empty($this->certificates)) {
            foreach ($this->certificates as $index => $certificate) {
                $filePath = $certificate->store('media/documents/certificates', 'public');

                // Get metadata for this certificate if available
                $title = isset($this->certificate_titles[$index]) ? $this->certificate_titles[$index] : $certificate->getClientOriginalName();
                $description = isset($this->certificate_descriptions[$index]) ? $this->certificate_descriptions[$index] : null;
                $expiryDate = isset($this->certificate_expiry_dates[$index]) ? $this->certificate_expiry_dates[$index] : null;

                Media::create([
                    'mediable_id' => $user->id,
                    'mediable_type' => User::class,
                    'file_name' => $certificate->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $certificate->getMimeType(),
                    'category' => 'certificate',
                    'uploaded_by' => Auth::id(),
                    'document_title' => $title,
                    'description' => $description,
                    'expiry_date' => $expiryDate,
                    'department' => $this->document_department,
                    'verification_status' => 'pending',
                ]);
            }
        }

        // Upload other documents with metadata
        if (!empty($this->other_documents)) {
            foreach ($this->other_documents as $index => $document) {
                $filePath = $document->store('media/documents/other', 'public');

                // Get metadata for this document if available
                $title = isset($this->other_document_titles[$index]) ? $this->other_document_titles[$index] : $document->getClientOriginalName();
                $description = isset($this->other_document_descriptions[$index]) ? $this->other_document_descriptions[$index] : null;
                $expiryDate = isset($this->other_document_expiry_dates[$index]) ? $this->other_document_expiry_dates[$index] : null;

                Media::create([
                    'mediable_id' => $user->id,
                    'mediable_type' => User::class,
                    'file_name' => $document->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $document->getMimeType(),
                    'category' => 'other_document',
                    'uploaded_by' => Auth::id(),
                    'document_title' => $title,
                    'description' => $description,
                    'expiry_date' => $expiryDate,
                    'department' => $this->document_department,
                    'verification_status' => 'pending',
                ]);
            }
        }

        $this->reset();
        session()->flash('message', 'User created successfully!');

        return $user;
    }

    public function update(User $user)
    {
        // Create custom rules for update
        $rules = $this->rules;
        $rules['campaign_id'] = ($this->canDisplayCampaign ? 'required' : 'nullable') . '|exists:campaigns,id';
        $rules['email'] = "required|email|unique:users,email,{$user->id}";
        $rules['new_password'] = 'nullable|string|min:8|confirmed';
        $rules['password_confirmation'] = 'nullable|required_with:new_password|string|min:8';

        // Remove registration_number validation for updates
        unset($rules['registration_number']);

        // Remove password rule for updates
        unset($rules['password']);

        // Validate with custom rules
        $data = $this->validate($rules, $this->messages);

        $role = Role::find($data['role_id']);
        $updateData = [
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'birth_date' => $data['birth_date'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'address' => $data['address'],
            'city' => $data['city'],
            'country' => $data['country'],
            'role_id' => $data['role_id'],
            'status' => $data['status'],
            'hire_date' => $data['hire_date'],
        ];

        // Only add campaign_id if it's a valid value
        if ($role->name === 'manager' && !empty($data['campaign_id'])) {
            // Verify the campaign exists
            $campaign = Campaign::find($data['campaign_id']);
            if ($campaign) {
                $updateData['campaign_id'] = $data['campaign_id'];
            }
        } else {
            $updateData['campaign_id'] = null;
        }

        // Only add department_id if it's a valid value
        if (!empty($data['department_id'])) {
            $department = Department::find($data['department_id']);
            if ($department) {
                $updateData['department_id'] = $data['department_id'];
            }
        } else {
            $updateData['department_id'] = null;
        }

        // Only add manager_id if it's a valid value
        if (!empty($data['manager_id'])) {
            $manager = User::find($data['manager_id']);
            if ($manager) {
                $updateData['manager_id'] = $data['manager_id'];
            }
        } else {
            $updateData['manager_id'] = null;
        }

        // Add other optional fields
        $updateData['job_title'] = $data['job_title'] ?? null;
        $updateData['employment_type'] = $data['employment_type'] ?? null;
        $updateData['hierarchy_level'] = $data['hierarchy_level'] ?? null;

        // Only add registration_number to updateData if it has a non-empty value
        if (!empty($this->registration_number)) {
            $updateData['registration_number'] = $this->registration_number;
        }

        if ($data['new_password']) {
            $updateData['password'] = bcrypt($data['new_password']);
        }

        // Use a direct query to update only the specified fields
        foreach ($updateData as $key => $value) {
            $user->$key = $value;
        }
        $user->save();

        // Update profile picture
        if ($this->profile_picture) {
            // Get the profile picture media model
            $profilePicture = $user->profilePicture()->first();

            // Check if a profile picture exists
            if ($profilePicture) {
                Storage::disk('public')->delete($profilePicture->file_path);
                $profilePicture->delete();
            }

            $filePath = $this->profile_picture->store('media/profile_pictures', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->profile_picture->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->profile_picture->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => Auth::id(),
            ]);
        }

        // Update resume
        if ($this->resume) {
            // Get existing resume if any
            $existingResume = $user->media()->where('category', 'resume')->first();

            // Prepare metadata to preserve
            $metadata = [
                'document_title' => null,
                'description' => null,
                'department' => null,
                'expiry_date' => null,
                'verification_status' => 'pending',
                'verified_by' => null,
                'verified_at' => null,
                'rejection_reason' => null
            ];

            // Preserve metadata from existing document if available
            if ($existingResume) {
                $metadata['document_title'] = $existingResume->document_title;
                $metadata['description'] = $existingResume->description;
                $metadata['department'] = $existingResume->department;
                $metadata['expiry_date'] = $existingResume->expiry_date;
                $metadata['verification_status'] = 'pending'; // Reset to pending on update

                // Delete the old file
                Storage::disk('public')->delete($existingResume->file_path);
                $existingResume->delete();
            }

            $filePath = $this->resume->store('media/documents/resumes', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->resume->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->resume->getMimeType(),
                'category' => 'resume',
                'uploaded_by' => Auth::id(),
                'document_title' => $metadata['document_title'],
                'description' => $metadata['description'],
                'department' => $metadata['department'],
                'expiry_date' => $metadata['expiry_date'],
                'verification_status' => $metadata['verification_status'],
                'verified_by' => $metadata['verified_by'],
                'verified_at' => $metadata['verified_at'],
                'rejection_reason' => $metadata['rejection_reason'],
            ]);
        }

        // Update ID card
        if ($this->id_card) {
            // Get existing ID card if any
            $existingIdCard = $user->media()->where('category', 'id_card')->first();

            // Prepare metadata to preserve
            $metadata = [
                'document_title' => null,
                'description' => null,
                'department' => null,
                'expiry_date' => null,
                'verification_status' => 'pending',
                'verified_by' => null,
                'verified_at' => null,
                'rejection_reason' => null
            ];

            // Preserve metadata from existing document if available
            if ($existingIdCard) {
                $metadata['document_title'] = $existingIdCard->document_title;
                $metadata['description'] = $existingIdCard->description;
                $metadata['department'] = $existingIdCard->department;
                $metadata['expiry_date'] = $existingIdCard->expiry_date;
                $metadata['verification_status'] = 'pending'; // Reset to pending on update

                // Delete the old file
                Storage::disk('public')->delete($existingIdCard->file_path);
                $existingIdCard->delete();
            }

            $filePath = $this->id_card->store('media/documents/id_cards', 'public');
            Media::create([
                'mediable_id' => $user->id,
                'mediable_type' => User::class,
                'file_name' => $this->id_card->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->id_card->getMimeType(),
                'category' => 'id_card',
                'uploaded_by' => Auth::id(),
                'document_title' => $metadata['document_title'],
                'description' => $metadata['description'],
                'department' => $metadata['department'],
                'expiry_date' => $metadata['expiry_date'],
                'verification_status' => $metadata['verification_status'],
                'verified_by' => $metadata['verified_by'],
                'verified_at' => $metadata['verified_at'],
                'rejection_reason' => $metadata['rejection_reason'],
            ]);
        }

        // Update certificates
        if (!empty($this->certificates)) {
            foreach ($this->certificates as $certificate) {
                $filePath = $certificate->store('media/documents/certificates', 'public');
                Media::create([
                    'mediable_id' => $user->id,
                    'mediable_type' => User::class,
                    'file_name' => $certificate->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $certificate->getMimeType(),
                    'category' => 'certificate',
                    'uploaded_by' => Auth::id(),
                    'verification_status' => 'pending',
                ]);
            }
        }

        // Update other documents
        if (!empty($this->other_documents)) {
            foreach ($this->other_documents as $document) {
                $filePath = $document->store('media/documents/other', 'public');
                Media::create([
                    'mediable_id' => $user->id,
                    'mediable_type' => User::class,
                    'file_name' => $document->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $document->getMimeType(),
                    'category' => 'other_document',
                    'uploaded_by' => Auth::id(),
                    'verification_status' => 'pending',
                ]);
            }
        }

        session()->flash('message', 'User updated successfully!');

        return $user;
    }

    public function removeProfilePicture($user)
    {
        if ($user->id) {
            $user = User::find($user->id);
            if ($user) {
                // Get the profile picture media model
                $profilePicture = $user->profilePicture()->first();

                // Check if a profile picture exists
                if ($profilePicture) {
                    Storage::disk('public')->delete($profilePicture->file_path);
                    $profilePicture->delete();

                    // Clear any cached profile picture URL
                    cache()->forget('user_profile_picture_' . $user->id);
                }
                $this->current_profile_picture = null; // Reset preview
            }
        }
    }

    /**
     * Remove a document from a user
     *
     * @param User $user The user
     * @param int $documentId The document ID
     * @param string $category The document category
     * @return void
     */
    public function removeDocument($user, $documentId, $category)
    {
        if ($user->id) {
            $user = User::find($user->id);
            if ($user) {
                $document = $user->media()->where('id', $documentId)->where('category', $category)->first();
                if ($document) {
                    Storage::disk('public')->delete($document->file_path);
                    $document->delete();

                    // Update current documents array
                    if (isset($this->current_documents[$category])) {
                        $this->current_documents[$category] = array_filter(
                            $this->current_documents[$category],
                            function($doc) use ($documentId) {
                                return $doc['id'] != $documentId;
                            }
                        );
                    }
                }
            }
        }
    }

    /**
     * Validate a single field for real-time validation
     *
     * @param string $field The field to validate
     * @param array|null $rules Custom rules to use
     * @param array $messages Custom messages to use
     * @param array $attributes Custom attributes to use
     * @param array $dataOverrides Data overrides
     * @return void
     */
    public function validateOnly($field, $rules = null, $messages = [], $attributes = [], $dataOverrides = [])
    {
        // If custom rules are not provided, use our defined rules
        if ($rules === null && isset($this->rules[$field])) {
            $customRules = [];
            $customRules[$field] = $this->rules[$field];

            // Add specific validation rule for campaign_id if role is manager
            if ($field === 'campaign_id' && $this->canDisplayCampaign) {
                $customRules[$field] = 'required|exists:campaigns,id';
            }

            // Add specific validation messages for this field
            $customMessages = [];
            if (isset($this->messages)) {
                foreach ($this->messages as $key => $message) {
                    if (strpos($key, $field . '.') === 0) {
                        $customMessages[$key] = $message;
                    }
                }
            }

            try {
                // Call parent validateOnly with our custom rules and messages
                parent::validateOnly($field, $customRules, $customMessages, $attributes, $dataOverrides);
            } catch (\Exception $e) {
                // Just catch the exception to prevent it from bubbling up
                // The validation errors will be available in the $errors bag
            }
        } else {
            // If custom rules are provided, use them
            parent::validateOnly($field, $rules, $messages, $attributes, $dataOverrides);
        }
    }
}
