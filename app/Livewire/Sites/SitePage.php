<?php

namespace App\Livewire\Sites;

use App\Livewire\Global\Page;
use App\Models\Site;
use App\Models\Platform;
use Livewire\Attributes\On;

class SitePage extends Page
{
    public ?Site $site = null;
    public ?Platform $platform = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-site-index')]
    public function toSiteIndex()
    {
        return $this->redirect(route('sites.index'), navigate: true);
    }

    #[On('to-site-show')]
    public function toSiteShow(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.show', ['site' => $site]), navigate: true);
    }

    #[On('to-site-edit')]
    public function toSiteEdit(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.edit', ['site' => $site]), navigate: true);
    }

    #[On('to-site-delete')]
    public function toSiteDelete(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.delete', ['site' => $site]), navigate: true);
    }

    #[On('to-site-create')]
    public function toSiteCreate()
    {
        return $this->redirect(route('sites.create'), navigate: true);
    }

    #[On('to-platform-create')]
    public function toPlatformCreate(Platform $platform)
    {
        if ($params && isset($site)) {
            return $this->redirect(route('platforms.create', ['site_id' => $site]), navigate: true);
        }
        return $this->redirect(route('platforms.create'), navigate: true);
    }

    // Site Platforms Submodule Navigation
    #[On('to-site-platforms')]
    public function toSitePlatforms(Platform $platform)
    {
        if (!$platform) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.platforms', ['site' => $platform]), navigate: true);
    }

    #[On('to-site-platforms-create')]
    public function toSitePlatformsCreate(Platform $platform)
    {
        if (!$platform) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.platforms.create', ['site' => $platform]), navigate: true);
    }

    #[On('to-site-platforms-edit')]
    public function toSitePlatformsEdit(Platform $platform)
    {
        if (!$platform) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.platforms.edit', ['site' => $platform, 'platform' => $params['platform']]), navigate: true);
    }

    #[On('to-site-platforms-show')]
    public function toSitePlatformsShow(Platform $platform)
    {
        if (!$platform) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.platforms.show', ['site' => $platform, 'platform' => $params['platform']]), navigate: true);
    }

    #[On('to-site-platforms-delete')]
    public function toSitePlatformsDelete(Platform $platform)
    {
        if (!$platform) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.platforms.delete', ['site' => $platform, 'platform' => $params['platform']]), navigate: true);
    }

    // Site Personnel Submodule Navigation
    #[On('to-site-personnel')]
    public function toSitePersonnel(Site $site, Platform $platform)
    {
        if ($params && isset($site)) {
            return $this->redirect(route('sites.personnel', ['site' => $site]), navigate: true);
        }
        return $this->redirect(route('sites.index'), navigate: true);
    }

    #[On('to-site-personnel-assign')]
    public function toSitePersonnelAssign(Site $site, Platform $platform)
    {
        if ($params && isset($site)) {
            return $this->redirect(route('sites.personnel.assign', ['site' => $site]), navigate: true);
        }
        return $this->redirect(route('sites.index'), navigate: true);
    }

    // Site Statistics Submodule Navigation
    #[On('to-site-statistics')]
    public function toSiteStatistics(Site $site, Platform $platform)
    {
        if ($params && isset($site)) {
            return $this->redirect(route('sites.statistics', ['site' => $site]), navigate: true);
        }
        return $this->redirect(route('sites.index'), navigate: true);
    }

    public function setPageResume($route)
    {
        // Set default values
        $this->resumeTitle = 'Sites';
        $this->resumeDescription = 'Site management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Sites',
            'description' => 'Site management system'
        ];

        switch ($route) {
            // Site Management
            case 'sites.index':
                $totalSites = Site::count();

                $this->resumeTitle = 'Sites Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Sites Management',
                    'description' => 'Manage all sites and their details',
                    'metrics' => [
                        ['label' => 'Total Sites', 'value' => $totalSites],
                        ['label' => 'Active Sites', 'value' => Site::where('status', 'active')->count()]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Sites Management';
                $this->current_page_resume['description'] = 'Manage all sites and their details';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'sites.create':
                $this->resumeTitle = 'Create New Site';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Site',
                    'description' => 'Add a new site to the system',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide site location and contact details',
                        'Set the site status (active/inactive)'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create New Site';
                $this->current_page_resume['description'] = 'Add a new site to the system';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'sites.edit':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = 'Edit: ' . $siteName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $siteName,
                    'description' => 'Modify site details',
                    'instructions' => [
                        'Update the necessary fields',
                        'You can change site location, contact details, and status'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Edit Site';
                $this->current_page_resume['description'] = 'Modify site details';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'sites.delete':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = 'Delete: ' . $siteName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $siteName,
                    'description' => 'Remove site from the system',
                    'warning' => 'This action cannot be undone. All associated platforms and personnel assignments will also be removed.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete Site';
                $this->current_page_resume['description'] = 'Remove site from the system';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'sites.show':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = $siteName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $siteName,
                    'subtitle' => 'Site Details',
                    'stats' => [
                        'Status' => $this->site ? ucfirst($this->site->status) : 'Unknown',
                        'Location' => $this->site ? $this->site->location : 'Not specified',
                        'Platforms' => $this->site ? $this->site->platforms()->count() : 0,
                        'Personnel' => $this->site ? $this->site->personnel()->count() : 0
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Site Details';
                $this->current_page_resume['description'] = 'View site information and platforms';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Platform Management
            case 'sites.platforms.index':
                $platformCount = $this->site ? $this->site->platforms()->count() : 0;

                $this->resumeTitle = 'Site Platforms';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Site Platforms',
                    'description' => 'Manage platforms for this site',
                    'metrics' => [
                        ['label' => 'Total Platforms', 'value' => $platformCount],
                        ['label' => 'Active Platforms', 'value' => $this->site ? $this->site->platforms()->where('status', 'active')->count() : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Site Platforms';
                $this->current_page_resume['description'] = 'Manage platforms for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'sites.platforms.create':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = 'Create Platform for ' . $siteName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Platform for ' . $siteName,
                    'description' => 'Add a new platform to this site',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Specify platform type and capabilities',
                        'Set the platform status'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Platform';
                $this->current_page_resume['description'] = 'Add a new platform to this site';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'sites.platforms.edit':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = 'Edit: ' . $platformName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $platformName,
                    'description' => 'Modify platform details',
                    'instructions' => [
                        'Update the necessary fields',
                        'You can change platform type, capabilities, and status'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Edit Platform';
                $this->current_page_resume['description'] = 'Modify platform details';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'sites.platforms.show':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = $platformName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $platformName,
                    'subtitle' => 'Platform Details',
                    'stats' => [
                        'Status' => $this->platform ? ucfirst($this->platform->status) : 'Unknown',
                        'Type' => $this->platform ? $this->platform->type : 'Not specified',
                        'Site' => $this->site ? $this->site->name : 'Unknown'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Platform Details';
                $this->current_page_resume['description'] = 'View platform information';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'sites.platforms.delete':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = 'Delete: ' . $platformName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $platformName,
                    'description' => 'Remove platform from the site',
                    'warning' => 'This action cannot be undone.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete Platform';
                $this->current_page_resume['description'] = 'Remove platform from the site';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Personnel Management
            case 'sites.personnels.index':
                $personnelCount = $this->site ? $this->site->personnel()->count() : 0;

                $this->resumeTitle = 'Site Personnel';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Site Personnel',
                    'description' => 'Manage personnel for this site',
                    'metrics' => [
                        ['label' => 'Total Personnel', 'value' => $personnelCount],
                        ['label' => 'Active Personnel', 'value' => $this->site ? $this->site->personnel()->where('status', 'active')->count() : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Site Personnel';
                $this->current_page_resume['description'] = 'Manage personnel for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'sites.personnels.assign':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = 'Assign Personnel to ' . $siteName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Assign Personnel to ' . $siteName,
                    'description' => 'Assign personnel to this site',
                    'instructions' => [
                        'Select personnel from the available list',
                        'Specify roles and responsibilities',
                        'Set assignment start and end dates if applicable'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Assign Personnel';
                $this->current_page_resume['description'] = 'Assign personnel to this site';
                $this->current_page_resume['type'] = 'chart';
                break;

            // Statistics
            case 'sites.statistics.index':
                $siteName = $this->site ? $this->site->name : 'Site';

                $this->resumeTitle = $siteName . ' Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => $siteName . ' Statistics',
                    'description' => 'View statistics for this site',
                    'metrics' => [
                        ['label' => 'Platforms', 'value' => $this->site ? $this->site->platforms()->count() : 0],
                        ['label' => 'Personnel', 'value' => $this->site ? $this->site->personnel()->count() : 0],
                        ['label' => 'Utilization', 'value' => '75', 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Site Statistics';
                $this->current_page_resume['description'] = 'View statistics for this site';
                $this->current_page_resume['type'] = 'chart';
                break;

            default:
                $this->resumeTitle = 'Sites';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Sites',
                    'description' => 'Site management system'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Sites';
                $this->current_page_resume['description'] = 'Manage sites';
                $this->current_page_resume['type'] = 'infos';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'site-module',
                'title' => 'Management',
                'description' => 'Site management',
                'route' => 'sites.index',
                'section_routes' => ['sites.create', 'sites.edit', 'sites.delete', 'sites.show'],
                'display' => true,
                'authorized_permissions' => ['manage_sites'],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a site',
                        'route' => 'sites.create',
                        'display' => true,
                        'authorized_permissions' => ['create_sites'],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a site',
                        'route' => 'sites.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_sites'],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a site',
                        'route' => 'sites.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_sites'],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a site',
                        'route' => 'sites.show',
                        'display' => true,
                        'authorized_permissions' => ['show_sites'],
                    ]
                ]
            ],
            [
                'module_id' => 'site-platforms-module',
                'title' => 'Platforms',
                'description' => 'Manage site platforms',
                'route' => 'sites.platforms.index',
                'section_routes' => ['sites.platforms.create', 'sites.platforms.edit', 'sites.platforms.delete', 'sites.platforms.show'],
                'display' => true,
                'authorized_permissions' => ['manage_site_platforms'],
                'sections' => [
                    [
                        'title' => 'All Platforms',
                        'description' => 'View all platforms',
                        'route' => 'sites.platforms',
                        'display' => true,
                        'authorized_permissions' => ['show_site_platforms'],
                    ],
                    [
                        'title' => 'Create Platform',
                        'description' => 'Create a new platform',
                        'route' => 'sites.platforms.create',
                        'display' => true,
                        'authorized_permissions' => ['create_site_platforms'],
                    ]
                ]
            ],
            [
                'module_id' => 'site-personnel-module',
                'title' => 'Personnel',
                'description' => 'Manage site personnel',
                'route' => 'sites.personnels.index',
                'section_routes' => ['sites.personnel.assign'],
                'display' => true,
                'authorized_permissions' => ['manage_site_personnel'],
                'sections' => [
                    [
                        'title' => 'View Personnel',
                        'description' => 'View site personnel',
                        'route' => 'sites.personnel',
                        'display' => true,
                        'authorized_permissions' => ['show_site_personnel'],
                    ],
                    [
                        'title' => 'Assign Personnel',
                        'description' => 'Assign personnel to site',
                        'route' => 'sites.personnel.assign',
                        'display' => true,
                        'authorized_permissions' => ['assign_site_personnel'],
                    ]
                ]
            ],
            [
                'module_id' => 'site-statistics-module',
                'title' => 'Statistics',
                'description' => 'Site statistics',
                'route' => 'sites.statistics.index',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['show_site_statistics'],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        return view('livewire.sites.site-page');
    }
}
