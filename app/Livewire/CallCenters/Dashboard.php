<?php

namespace App\Livewire\CallCenters;

use App\Models\CallCenter;
use App\Models\Department;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class Dashboard extends Component
{
    public $totalCallCenters;
    public $activeCenters;
    public $totalSites;
    public $totalDepartments;
    public $totalStaff;
    public $recentCallCenters;

    public function mount()
    {
        $this->loadStatistics();
    }

    public function loadStatistics()
    {
        // Get call center statistics
        $this->totalCallCenters = CallCenter::count();
        $this->activeCenters = CallCenter::where('status', 'active')->count();
        $this->totalSites = Site::count();
        $this->totalDepartments = Department::count();

        // Get staff count (users assigned to call centers)
        $this->totalStaff = User::whereHas('role', function($query) {
            $query->whereIn('name', ['Agent', 'Supervisor', 'Manager']);
        })->count();

        // Get recent call centers
        $this->recentCallCenters = CallCenter::with('director')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
    }

    public function navigateToCallCenter($callCenterId)
    {
        return redirect()->route('call-centers.show', ['callCenter' => $callCenterId]);
    }

    public function getListeners()
    {
        return [
            'navigate-to' => 'handleNavigation',
        ];
    }

    public function handleNavigation($data)
    {
        if (isset($data['route'])) {
            return redirect()->route($data['route'], $data['params'] ?? []);
        }
    }

    public function render()
    {
        return view('livewire.call-centers.dashboard');
    }
}
