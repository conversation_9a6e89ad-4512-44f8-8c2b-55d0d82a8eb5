<?php

namespace App\Livewire\CallCenters;

use App\Livewire\Global\Page;
use Illuminate\Support\Facades\Route;

class CallCenterPage extends Page
{
    public $component = '';
    public $callCenter;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_section = [];
    public array $current_page_resume = []; // For backward compatibility
    public $current_route = '';
    public array $current_module = [];

    public function mount($component = '')
    {
        // Get the current route name
        $routeName = Route::currentRouteName();

        // Determine the component name based on the route
        $componentStr = '';

        if ($routeName) {
            // Extract the component name from the route
            $parts = explode('.', $routeName);
            if (count($parts) >= 2) {
                // For routes like call-centers.index, call-centers.sites.index, etc.
                if ($parts[0] === 'call-centers') {
                    if (count($parts) === 2) {
                        // Main call center routes
                        $componentStr = 'call-centers.call-center-' . $parts[1];
                    } else if (count($parts) >= 3) {
                        // Submodule routes (sites, departments)
                        $componentStr = 'call-centers.' . $parts[1] . '.' . $parts[1] . '-' . $parts[2];
                    }
                }
            }
        }

        // If we couldn't determine the component from the route, use the provided component if it's a string
        if (empty($componentStr) && is_string($component)) {
            $componentStr = $component;
        }

        // Call parent mount with the determined component
        parent::mount($componentStr);

        // Store the component value
        $this->component = $componentStr;

        // Get callCenter from route if available
        $callCenterParam = request()->route('callCenter');

        // Ensure callCenter is a CallCenter object
        if ($callCenterParam instanceof \App\Models\CallCenter) {
            $this->callCenter = $callCenterParam;
        } else {
            // Try to find the CallCenter by ID if it's a numeric value
            if (is_numeric($callCenterParam)) {
                $this->callCenter = \App\Models\CallCenter::find($callCenterParam);

                // No need to log if call center not found
            } else {
                $this->callCenter = null;
            }
        }

        // Ensure current_route is a string
        $routeName = Route::currentRouteName();
        $this->current_route = is_string($routeName) ? $routeName : '';

        // Define pages for the Call Centers module
        $this->pages = [
            [
                'title' => 'Dashboard',
                'description' => 'Call Center Dashboard',
                'route' => 'call-centers.index',
                'display' => true, // Display in the sidebar
                'authorized_roles' => [1, 2, 3, 4, 11, 12], // Admin, Director, Manager, Supervisor, Accountant, HR Manager
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Call Centers',
                'description' => 'Manage call centers',
                'route' => 'call-centers.list',
                'display' => true, // Display in the sidebar
                'authorized_roles' => [1, 2, 3, 4], // Admin, Director, Manager, Supervisor
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Create',
                'description' => 'Create a new call center',
                'route' => 'call-centers.create',
                'display' => true,
                'authorized_roles' => [1, 2], // Admin, Director
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Edit',
                'description' => 'Edit call center',
                'route' => 'call-centers.edit',
                'display' => true,
                'authorized_roles' => [1, 2], // Admin, Director
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Show',
                'description' => 'View call center details',
                'route' => 'call-centers.show',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4], // Admin, Director, Manager, Supervisor
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Delete',
                'description' => 'Delete call center',
                'route' => 'call-centers.delete',
                'display' => true,
                'authorized_roles' => [1, 2], // Admin, Director
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Sites',
                'description' => 'Manage call center sites',
                'route' => 'call-centers.sites.index',
                'display' => true, // Hide from sidebar
                'authorized_roles' => [1, 2, 3, 4, 11, 12], // Admin, Director, Manager, Supervisor, Accountant, HR Manager
                'section_routes' => [
                    'call-centers.sites.create',
                    'call-centers.sites.edit',
                    'call-centers.sites.show',
                    'call-centers.sites.delete',
                    'call-centers.sites.platforms.index',
                    'call-centers.sites.personnels.index',
                    'call-centers.sites.reports.index',
                    'call-centers.sites.equipments.index'
                ],
                'sections' => []
            ],
            [
                'title' => 'Departments',
                'description' => 'Manage call center departments',
                'route' => 'call-centers.departments.index',
                'display' => true, // Hide from sidebar
                'authorized_roles' => [1, 2, 3, 12], // Admin, Director, Manager, HR Manager
                'section_routes' => [
                    'call-centers.departments.create',
                    'call-centers.departments.edit',
                    'call-centers.departments.show',
                    'call-centers.departments.delete'
                ],
                'sections' => []
            ]
        ];

        // Get current page and section
        $this->current_page = $this->getCurrentPage($this->pages);

        // Initialize empty section since we don't have sections in call centers
        $this->current_page_section = [];

        // Set page resume
        $this->current_page_resume = [
            'title' => $this->current_page['title'] ?? 'Call Centers',
            'description' => $this->current_page['description'] ?? 'Manage your call centers',
            'icon' => 'phone',
        ];

        // Set dynamic page resume
        $this->setPageResume($this->current_route);

        // Set current module
        $this->current_module = [
            'title' => 'Call Centers',
            'routes' => [
                'call-centers.index',
                'call-centers.create',
                'call-centers.edit',
                'call-centers.show',
                'call-centers.delete',
                'call-centers.sites.index',
                'call-centers.sites.create',
                'call-centers.sites.edit',
                'call-centers.sites.show',
                'call-centers.sites.delete',
                'call-centers.departments.index',
                'call-centers.departments.create',
                'call-centers.departments.edit',
                'call-centers.departments.show',
                'call-centers.departments.delete'
            ],
        ];
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Call Centers';
        $this->resumeDescription = 'Call center management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Call Centers',
            'description' => 'Comprehensive call center management system for overseeing all operational aspects of your call centers. This module allows you to manage multiple call center locations, their sites, departments, and related resources.'
        ];

        switch ($routeName) {
            case 'call-centers.index':
                $totalCallCenters = \App\Models\CallCenter::count();
                $activeCallCenters = \App\Models\CallCenter::where('status', 'active')->count();
                $totalSites = \App\Models\Site::count();
                $totalDepartments = \App\Models\Department::count();

                $this->resumeTitle = 'Call Center Dashboard';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Call Center Dashboard',
                    'description' => 'Comprehensive overview of all call centers in the organization. This dashboard provides key metrics and insights into call center operations, allowing you to monitor performance, resource allocation, and operational status across all locations.',
                    'metrics' => [
                        ['label' => 'Total Call Centers', 'value' => $totalCallCenters, 'change' => null],
                        ['label' => 'Active Call Centers', 'value' => $activeCallCenters, 'change' => $totalCallCenters > 0 ? round(($activeCallCenters / $totalCallCenters) * 100) : 0],
                        ['label' => 'Total Sites', 'value' => $totalSites, 'change' => null],
                        ['label' => 'Total Departments', 'value' => $totalDepartments, 'change' => null]
                    ]
                ];
                break;

            case 'call-centers.list':
                $totalCallCenters = \App\Models\CallCenter::count();
                $activeCallCenters = \App\Models\CallCenter::where('status', 'active')->count();
                $inactiveCallCenters = $totalCallCenters - $activeCallCenters;

                $this->resumeTitle = 'Call Centers';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Call Centers Management',
                    'description' => 'Manage all call centers in the organization. This page provides a complete listing of all call centers with filtering and search capabilities. Use this interface to view, create, edit, and manage call center records, including their operational status and key metrics.',
                    'metrics' => [
                        ['label' => 'Total Call Centers', 'value' => $totalCallCenters, 'change' => null],
                        ['label' => 'Active Call Centers', 'value' => $activeCallCenters, 'change' => $totalCallCenters > 0 ? round(($activeCallCenters / $totalCallCenters) * 100) : 0],
                        ['label' => 'Inactive Call Centers', 'value' => $inactiveCallCenters, 'change' => null],
                        ['label' => 'Agents Assigned', 'value' => \App\Models\User::whereIn('role_id', [5, 6])->count(), 'change' => null]
                    ]
                ];
                break;

            case 'call-centers.create':
                $this->resumeTitle = 'Create Call Center';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Call Center',
                    'description' => 'Add a new call center to the organization. This form allows you to set up a new call center with all necessary information including location, contact details, operational parameters, and administrative settings.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide detailed call center location and contact information',
                        'Set the call center status (active/inactive)',
                        'Specify operational hours and capacity',
                        'Assign management personnel if applicable',
                        'You can add sites and departments after creating the call center'
                    ]
                ];
                break;

            case 'call-centers.edit':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';

                $this->resumeTitle = 'Edit: ' . $callCenterName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $callCenterName,
                    'description' => 'Modify details and settings for ' . $callCenterName . '. This form allows you to update all aspects of the call center configuration, including location information, contact details, operational parameters, and administrative settings.',
                    'instructions' => [
                        'Update only the fields that need to be changed',
                        'You can modify call center location, contact details, and operational parameters',
                        'Changing the status will affect the call center\'s visibility and functionality in the system',
                        'Management personnel assignments can be updated if needed',
                        'Sites and departments can be managed from their respective sections'
                    ]
                ];
                break;

            case 'call-centers.show':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';
                $siteCount = $this->callCenter ? $this->callCenter->sites()->count() : 0;
                $departmentCount = $this->callCenter ? $this->callCenter->departments()->count() : 0;
                $activeSites = $this->callCenter ? $this->callCenter->sites()->where('status', 'active')->count() : 0;
                $activeDepartments = $this->callCenter ? $this->callCenter->departments()->where('status', 'active')->count() : 0;

                $this->resumeTitle = $callCenterName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $callCenterName,
                    'subtitle' => 'Call Center Details',
                    'description' => 'Detailed information about ' . $callCenterName . '. This page provides comprehensive details about the call center\'s configuration, operational status, and associated resources. Use this information to monitor and manage all aspects of the call center.',
                    'stats' => [
                        'Status' => $this->callCenter ? ucfirst($this->callCenter->status) : 'Unknown',
                        'Location' => $this->callCenter ? $this->callCenter->location : 'Not specified',
                        'Sites' => $siteCount . ' (' . $activeSites . ' active)',
                        'Departments' => $departmentCount . ' (' . $activeDepartments . ' active)',
                        'Created' => $this->callCenter && $this->callCenter->created_at ? $this->callCenter->created_at->format('M d, Y') : 'Unknown',
                        'Last Updated' => $this->callCenter && $this->callCenter->updated_at ? $this->callCenter->updated_at->format('M d, Y') : 'Unknown',
                        'Manager' => $this->callCenter && $this->callCenter->manager ? $this->callCenter->manager->getFullNameAttribute() : 'Not assigned'
                    ]
                ];
                break;

            case 'call-centers.delete':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';

                $this->resumeTitle = 'Delete: ' . $callCenterName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $callCenterName,
                    'description' => 'You are about to permanently remove ' . $callCenterName . ' from the system. This action will delete the call center record and all associated data.',
                    'warning' => 'This action cannot be undone. All associated sites, departments, and related data will also be removed. Consider deactivating the call center instead if you may need to restore it in the future.',
                    'stats' => [
                        'Status' => $this->callCenter ? ucfirst($this->callCenter->status) : 'Unknown',
                        'Sites' => $this->callCenter ? $this->callCenter->sites()->count() : 0,
                        'Departments' => $this->callCenter ? $this->callCenter->departments()->count() : 0,
                        'Created' => $this->callCenter && $this->callCenter->created_at ? $this->callCenter->created_at->format('M d, Y') : 'Unknown'
                    ]
                ];
                break;

            case 'call-centers.sites.index':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';
                $siteCount = $this->callCenter ? $this->callCenter->sites()->count() : 0;
                $activeSites = $this->callCenter ? $this->callCenter->sites()->where('status', 'active')->count() : 0;
                $inactiveSites = $siteCount - $activeSites;

                $this->resumeTitle = $callCenterName . ' - Sites';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => $callCenterName . ' - Sites',
                    'description' => 'Manage physical locations and operational sites for ' . $callCenterName . '. This page allows you to view, create, edit, and manage all sites associated with this call center. Sites represent distinct physical locations where call center operations take place.',
                    'metrics' => [
                        ['label' => 'Total Sites', 'value' => $siteCount, 'change' => null],
                        ['label' => 'Active Sites', 'value' => $activeSites, 'change' => $siteCount > 0 ? round(($activeSites / $siteCount) * 100) : 0],
                        ['label' => 'Inactive Sites', 'value' => $inactiveSites, 'change' => null],
                        ['label' => 'Capacity', 'value' => $this->callCenter ? $this->callCenter->sites()->sum('capacity') : 0, 'change' => null]
                    ]
                ];
                break;

            case 'call-centers.sites.create':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';

                $this->resumeTitle = 'Create Site for ' . $callCenterName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Site for ' . $callCenterName,
                    'description' => 'Add a new operational site to ' . $callCenterName . '. This form allows you to set up a new physical location with all necessary information including address, contact details, capacity, and operational parameters.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide detailed site location and contact information',
                        'Specify the site\'s operational capacity and resources',
                        'Set the site status (active/inactive)',
                        'Assign site management personnel if applicable',
                        'You can add equipment and platform details after creating the site'
                    ]
                ];
                break;

            case 'call-centers.departments.index':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';
                $departmentCount = $this->callCenter ? $this->callCenter->departments()->count() : 0;
                $activeDepartments = $this->callCenter ? $this->callCenter->departments()->where('status', 'active')->count() : 0;
                $inactiveDepartments = $departmentCount - $activeDepartments;

                $this->resumeTitle = $callCenterName . ' - Departments';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => $callCenterName . ' - Departments',
                    'description' => 'Manage organizational departments for ' . $callCenterName . '. This page allows you to view, create, edit, and manage all departments within this call center. Departments represent functional divisions with specific responsibilities and personnel.',
                    'metrics' => [
                        ['label' => 'Total Departments', 'value' => $departmentCount, 'change' => null],
                        ['label' => 'Active Departments', 'value' => $activeDepartments, 'change' => $departmentCount > 0 ? round(($activeDepartments / $departmentCount) * 100) : 0],
                        ['label' => 'Inactive Departments', 'value' => $inactiveDepartments, 'change' => null],
                        ['label' => 'Staff Assigned', 'value' => $this->callCenter ? \App\Models\User::whereHas('department', function($query) {
                            $query->where('call_center_id', $this->callCenter->id);
                        })->count() : 0, 'change' => null]
                    ]
                ];
                break;

            case 'call-centers.departments.create':
                $callCenterName = $this->callCenter ? $this->callCenter->name : 'Call Center';

                $this->resumeTitle = 'Create Department for ' . $callCenterName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create Department for ' . $callCenterName,
                    'description' => 'Add a new department to ' . $callCenterName . '. This form allows you to set up a new functional division with all necessary information including name, purpose, responsibilities, and management structure.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Specify the department\'s function and responsibilities',
                        'Set the department status (active/inactive)',
                        'Assign department head and management personnel',
                        'Define the department\'s position in the organizational hierarchy',
                        'You can assign staff to the department after creation'
                    ]
                ];
                break;

            default:
                $this->resumeTitle = 'Call Centers';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Call Centers',
                    'description' => 'Comprehensive call center management system for overseeing all operational aspects of your call centers. This module allows you to manage multiple call center locations, their sites, departments, and related resources.'
                ];
                break;
        }
    }

    public function render()
    {
        $data = [
            'callCenter' => $this->callCenter
        ];

        // Add site to the data if the component is related to sites
        if (strpos($this->component, 'sites') !== false) {
            $site = request()->route('site');
            if ($site) {
                $data['site'] = $site;
            }
        }

        // Add department to the data if the component is related to departments
        if (strpos($this->component, 'departments') !== false) {
            $department = request()->route('department');
            if ($department) {
                $data['department'] = $department;
            }
        }

        return view('livewire.call-centers.call-center-page', $data);
    }
}
