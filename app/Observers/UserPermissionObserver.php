<?php

namespace App\Observers;

use App\Models\UserPermission;
use App\Services\PermissionCacheService;
use App\Models\User;

class UserPermissionObserver
{
    /**
     * Handle the UserPermission "created" event.
     */
    public function created(UserPermission $userPermission): void
    {
        $this->clearCacheForUser($userPermission->user_id);
    }

    /**
     * Handle the UserPermission "updated" event.
     */
    public function updated(UserPermission $userPermission): void
    {
        $this->clearCacheForUser($userPermission->user_id);
    }

    /**
     * Handle the UserPermission "deleted" event.
     */
    public function deleted(UserPermission $userPermission): void
    {
        $this->clearCacheForUser($userPermission->user_id);
    }

    /**
     * Clear cache for the given user.
     */
    private function clearCacheForUser(int $userId): void
    {
        $user = User::find($userId);
        
        if ($user) {
            PermissionCacheService::clearUserPermissionsCache($user);
        }
    }
}
