<?php

namespace App\Observers;

use App\Models\RolePermission;
use App\Services\PermissionCacheService;
use App\Models\User;

class RolePermissionObserver
{
    /**
     * Handle the RolePermission "created" event.
     */
    public function created(RolePermission $rolePermission): void
    {
        $this->clearCacheForRole($rolePermission->role_id);
    }

    /**
     * Handle the RolePermission "updated" event.
     */
    public function updated(RolePermission $rolePermission): void
    {
        $this->clearCacheForRole($rolePermission->role_id);
    }

    /**
     * Handle the RolePermission "deleted" event.
     */
    public function deleted(RolePermission $rolePermission): void
    {
        $this->clearCacheForRole($rolePermission->role_id);
    }

    /**
     * Clear cache for all users with the given role.
     */
    private function clearCacheForRole(int $roleId): void
    {
        $users = User::where('role_id', $roleId)->get();
        
        foreach ($users as $user) {
            PermissionCacheService::clearUserPermissionsCache($user);
        }
    }
}
