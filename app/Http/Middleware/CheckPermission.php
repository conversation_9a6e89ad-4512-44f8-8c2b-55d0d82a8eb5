<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Log;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$permissions
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        // If user is not authenticated, redirect to login
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Super admin has all permissions
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // If no specific permissions are required, check route-based permissions
        if (empty($permissions)) {
            $permissions = $this->getPermissionsForRoute($request);
            
            // If no permissions are required for this route, allow access
            if (empty($permissions)) {
                return $next($request);
            }
        }

        // Check if user has any of the required permissions
        if ($user->hasAnyPermission($permissions)) {
            return $next($request);
        }

        // Log unauthorized access attempt
        Log::warning('Unauthorized access attempt', [
            'user_id' => $user->id,
            'email' => $user->email,
            'route' => $request->route()->getName(),
            'required_permissions' => $permissions,
            'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray()
        ]);

        // If user doesn't have any of the required permissions
        if ($user->hasRole('Agent')) {
            return redirect()
                ->route('appointments.index')
                ->with('error', 'You do not have permission to access that page.');
        }

        // For other unauthorized users, show 403
        abort(403, 'You do not have permission to access this page.');
    }

    /**
     * Get permissions required for the current route
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    protected function getPermissionsForRoute(Request $request): array
    {
        $routeName = $request->route()->getName();
        
        if (!$routeName) {
            return [];
        }

        // Try to find a permission that matches the route name
        $permission = Permission::where('name', 'like', "%{$routeName}%")->first();
        
        if ($permission) {
            return [$permission->name];
        }

        // Try to find module access permission based on route name
        $modules = config('modules', []);
        foreach ($modules as $module) {
            if (in_array($routeName, $module['routes'] ?? [])) {
                return ['access_' . $module['id']];
            }
        }

        return [];
    }
}
