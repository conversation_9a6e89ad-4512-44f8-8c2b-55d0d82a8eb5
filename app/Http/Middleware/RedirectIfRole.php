<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $roleId, $routeName): Response
    {
        if (Auth::check()) {
            // Check if roleId is a comma-separated list
            if (strpos($roleId, ',') !== false) {
                $roleIds = explode(',', $roleId);
                if (in_array(Auth::user()->role_id, $roleIds)) {
                    return redirect()->route($routeName);
                }
            }
            // Check for single role ID
            else if (Auth::user()->role_id == $roleId) {
                return redirect()->route($routeName);
            }
        }
        return $next($request);
    }
}
