<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!Auth::check()) {
            return redirect('login');
        }

        $user = Auth::user();

        // If no specific roles are required, or user is admin, allow access
        if (empty($roles) || $user->hasRole('Admin')) {
            return $next($request);
        }

        // Check if user has any of the required roles
        if ($user->hasAnyRole($roles)) {
            return $next($request);
        }

        // If user doesn't have any of the required roles
        if ($user->hasRole('Agent')) {
            // Agents get redirected to appointments
            return redirect()->route('appointments.index');
        }

        // Other unauthorized roles
        abort(403, 'Unauthorized action.');
    }
}
