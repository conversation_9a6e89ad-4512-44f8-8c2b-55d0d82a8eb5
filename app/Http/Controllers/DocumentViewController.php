<?php

namespace App\Http\Controllers;

use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DocumentViewController extends Controller
{
    /**
     * Display the specified document.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $document = Media::findOrFail($id);

        // Check if user has permission to view this document
        if (!$this->canViewDocument($document)) {
            abort(403, 'Unauthorized action.');
        }

        // Check if file exists
        if (!Storage::disk('public')->exists($document->file_path)) {
            abort(404, 'File not found.');
        }

        $path = Storage::disk('public')->path($document->file_path);
        $mimeType = $document->mime_type ?: mime_content_type($path);

        // For images and PDFs, display in browser
        if (in_array($mimeType, ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'])) {
            return response()->file($path, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'inline; filename="' . $document->file_name . '"'
            ]);
        }

        // For other file types, force download
        return response()->download($path, $document->file_name, [
            'Content-Type' => $mimeType
        ]);
    }

    /**
     * Check if the current user can view the document.
     *
     * @param  Media  $document
     * @return bool
     */
    protected function canViewDocument(Media $document)
    {
        $user = auth()->user();

        // Profile pictures are always accessible to all authenticated users
        if ($document->category === 'profile_picture') {
            return true;
        }

        // Admin can view all documents
        if ($user->role_id === 1) {
            return true;
        }

        // HR Manager can view all user documents
        if ($user->role_id === 12 && $document->mediable_type === 'App\\Models\\User') {
            return true;
        }

        // Users can view their own documents
        if ($document->mediable_type === 'App\\Models\\User' && $document->mediable_id === $user->id) {
            return true;
        }

        // Managers can view documents of users in their campaign
        if (in_array($user->role_id, [2, 3, 4, 5]) && $document->mediable_type === 'App\\Models\\User') {
            $documentUser = \App\Models\User::find($document->mediable_id);
            if ($documentUser && $documentUser->campaign_id === $user->campaign_id) {
                return true;
            }
        }

        return false;
    }
}
