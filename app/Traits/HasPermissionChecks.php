<?php

namespace App\Traits;

trait HasPermissionChecks
{
    /**
     * Check if user has any of the given permissions
     *
     * @param array|string $permissions
     * @return bool
     */
    public function hasAnyPermission($permissions): bool
    {
        if (auth()->user()->isSuperAdmin()) {
            return true;
        }

        return auth()->user()->hasAnyPermission($permissions);
    }

    /**
     * Check if user has all of the given permissions
     *
     * @param array|string $permissions
     * @return bool
     */
    public function hasAllPermissions($permissions): bool
    {
        if (auth()->user()->isSuperAdmin()) {
            return true;
        }

        return auth()->user()->hasAllPermissions($permissions);
    }

    /**
     * Check if user can access a module
     * 
     * @param string $moduleId
     * @return bool
     */
    public function canAccessModule(string $moduleId): bool
    {
        return auth()->user()->can('access_' . $moduleId);
    }

    /**
     * Filter menu items based on permissions
     * 
     * @param array $menuItems
     * @return array
     */
    public function filterMenuItems(array $menuItems): array
    {
        return array_filter($menuItems, function ($item) {
            // Check if user has access to the module
            if (isset($item['id']) && !$this->canAccessModule($item['id'])) {
                return false;
            }

            // Check if there are specific permissions required
            if (isset($item['permissions'])) {
                $permissions = is_array($item['permissions']) 
                    ? $item['permissions'] 
                    : explode(',', str_replace(' ', '', $item['permissions']));
                
                if (!$this->hasAnyPermission($permissions)) {
                    return false;
                }
            }

            // Check sub-items if they exist
            if (isset($item['children'])) {
                $item['children'] = $this->filterMenuItems($item['children']);
                
                // If no children are left after filtering, remove the parent
                if (empty($item['children'])) {
                    return false;
                }
            }

            return true;
        });
    }
}
