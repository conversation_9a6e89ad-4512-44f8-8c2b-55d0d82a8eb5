<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Map your models to their policies.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        \App\Models\User::class       => \App\Policies\UserPolicy::class,
        \App\Models\Role::class       => \App\Policies\RolePolicy::class,
        \App\Models\Permission::class => \App\Policies\PermissionPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Dynamically define module- and permission-based gates
        foreach (config('modules') as $moduleId => $module) {
            // Gate: access_{moduleId}
            Gate::define("access_{$moduleId}", function ($user) use ($moduleId, $module) {
                // 1) has the explicit "access_{moduleId}" permission?
                // OR 2) has _any_ of this module's granular permissions?
                return $user->getPermissionNames("access_{$moduleId}") ||
                       $user->hasAnyPermission($module['permissions']);
            });

            // Gates for each individual permission under this module
            foreach ($module['permissions'] as $permission) {
                Gate::define($permission, fn($user) => $user->getPermissionNames($permission));
            }
        }

        // Super-admin shortcut
        Gate::define('super_admin', fn($user) => $user->hasRole('super_admin'));

        // Resource gates (automatically maps index/view/create/update/delete)
        Gate::resource('user',       \App\Policies\UserPolicy::class);
        Gate::resource('role',       \App\Policies\RolePolicy::class);
        Gate::resource('permission', \App\Policies\PermissionPolicy::class);
    }
}
