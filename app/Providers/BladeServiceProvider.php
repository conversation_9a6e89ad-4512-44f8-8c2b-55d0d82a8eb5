<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }


    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Custom @permission directive
        Blade::if('permission', function ($permissions) {
            return hasAnyPermission($permissions);
        });

        // Custom @module directive
        Blade::if('module', function ($moduleId) {
            return canAccessModule($moduleId);
        });

        // Custom @role directive
        Blade::if('role', function ($role) {
            return auth()->check() && auth()->user()->hasRole($role);
        });

        // Custom @anyrole directive
        Blade::if('anyrole', function ($roles) {
            if (!auth()->check()) {
                return false;
            }
            
            $roles = is_array($roles) ? $roles : func_get_args();
            return auth()->user()->hasAnyRole($roles);
        });
    }
}
