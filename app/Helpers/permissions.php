<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

if (! function_exists('has_any_permission')) {
    /**
     * Check if the authenticated user has any of the given permissions
     *
     * @param  string|array  $permissions
     * @return bool
     */
    function has_any_permission($permissions)
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }
        
        // Super admin has all permissions
        if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
            return true;
        }
        
        return $user->hasAnyPermission((array) $permissions);
    }
}

if (! function_exists('has_all_permissions')) {
    /**
     * Check if the authenticated user has all of the given permissions
     *
     * @param  string|array  $permissions
     * @return bool
     */
    function has_all_permissions($permissions)
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }
        
        // Super admin has all permissions
        if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
            return true;
        }
        
        return $user->hasAllPermissions((array) $permissions);
    }
}

// Alias for backward compatibility
if (! function_exists('has_permission')) {
    function has_permission($permission)
    {
        return has_any_permission($permission);
    }
}

if (! function_exists('can_access_module')) {
    /**
     * Check if the authenticated user can access a module
     *
     * @param  string  $moduleId
     * @return bool
     */
    function can_access_module($moduleId)
    {
        return has_permission('access_' . $moduleId);
    }
}

if (! function_exists('module_route')) {
    /**
     * Generate a route if the user has permission to access it
     *
     * @param  string  $name
     * @param  mixed  $parameters
     * @param  bool  $absolute
     * @return string|null
     */
    function module_route($name, $parameters = [], $absolute = true)
    {
        if (!has_any_permission($name)) {
            return null;
        }
        
        try {
            return route($name, $parameters, $absolute);
        } catch (\Exception $e) {
            Log::error("Failed to generate route: " . $e->getMessage(), [
                'route' => $name,
                'parameters' => $parameters,
                'absolute' => $absolute
            ]);
            return null;
        }
    }
}

if (! function_exists('module_asset')) {
    /**
     * Generate an asset URL if the user has permission to access the module
     *
     * @param  string  $path
     * @param  string|null  $moduleId
     * @return string|null
     */
    function module_asset($path, $moduleId = null)
    {
        if ($moduleId && !can_access_module($moduleId)) {
            return null;
        }
        
        return asset($path);
    }
}

if (! function_exists('module_link')) {
    /**
     * Generate a link if the user has permission to access the route
     *
     * @param  string  $name
     * @param  string  $title
     * @param  array  $parameters
     * @param  array  $attributes
     * @param  bool  $escape
     * @return string
     */
    function module_link($name, $title = null, $parameters = [], $attributes = [], $escape = true)
    {
        $url = module_route($name, $parameters);
        
        if (!$url) {
            return '';
        }
        
        return '<a href="' . e($url) . '"' . 
               ($attributes ? ' ' . html_attributes($attributes) : '') . 
               '>' . ($escape ? e($title ?? $name) : $title ?? $name) . '</a>';
    }
}
