<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;
use App\Models\Role;

class ModuleAccess
{
    /**
     * Check if the current user can access a module.
     *
     * @param string $moduleId
     * @return bool
     */
    public static function canAccess(string $moduleId): bool
    {
        if (!Auth::check()) {
            return false;
        }
        
        return Auth::user()->canAccessModule($moduleId);
    }
    
    /**
     * Get all modules the current user can access.
     *
     * @return array
     */
    public static function getAccessibleModules(): array
    {
        if (!Auth::check()) {
            return [];
        }
        
        $user = Auth::user();
        $modules = config('modules');
        
        return collect($modules)
            ->filter(function ($module) use ($user) {
                return $user->canAccessModule($module['id']);
            })
            ->toArray();
    }
}
