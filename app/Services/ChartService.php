<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Report;

class ChartService
{
    /**
     * Get user statistics for dashboard
     *
     * @param string $period
     * @return array
     */
    public function getUserStats(string $period = 'week'): array
    {
        [$start, $end, $prevStart, $prevEnd] = $this->getDateRanges($period);

        $currentCount  = \App\Models\User::whereBetween('created_at', [$start, $end])->count();
        $previousCount = \App\Models\User::whereBetween('created_at', [$prevStart, $prevEnd])->count();
        $growth = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 1)
            : 0;

        $roleDistribution   = $this->groupByCount(\App\Models\User::class, 'role_id');
        $statusDistribution = $this->groupByCount(\App\Models\User::class, 'status');

        $dailyData = $this->groupCountByDate(\App\Models\User::class, 'created_at', $start, $end);
        $chartData = $this->formatDailyChartData($dailyData, 'New Users', $start, $end);

        return [
            'title' => 'Users',
            'currentCount' => $currentCount,
            'previousCount' => $previousCount,
            'growth' => $growth,
            'count' => \App\Models\User::count(),
            'current_period' => $currentCount,
            'growth_percentage' => $growth,
            'role_distribution' => $roleDistribution,
            'statusDistribution' => $statusDistribution,
            'dailyData' => $dailyData,
            'chartData' => $chartData,
            // Add these keys to match what the templates expect
            'monthly_campaigns' => [],
            'agents_by_campaign' => [],
            'status_distribution' => $statusDistribution,
            'byCustomer' => []
        ];
    }

    /**
     * Get agent statistics for dashboard
     *
     * @param string $period
     * @return array
     */
    public function getAgentStats(string $period = 'week'): array
    {
        [$start, $end, $prevStart, $prevEnd] = $this->getDateRanges($period);

        $roleId = config('roles.agent_id', 6);
        $currentCount  = \App\Models\User::where('role_id', $roleId)->whereBetween('created_at', [$start, $end])->count();
        $previousCount = \App\Models\User::where('role_id', $roleId)->whereBetween('created_at', [$prevStart, $prevEnd])->count();
        $growth = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 1)
            : 0;

        $statusDistribution = \App\Models\User::where('role_id', $roleId)
            ->groupBy('status')
            ->pluck(DB::raw('count(*)'), 'status')
            ->toArray();

        $byCampaign = \App\Models\User::where('role_id', $roleId)
            ->join('campaigns', 'users.campaign_id', '=', 'campaigns.id')
            ->groupBy('campaigns.name')
            ->pluck(DB::raw('count(*)'), 'campaigns.name')
            ->toArray();

        $dailyData = $this->groupCountByDate(\App\Models\Shift::class, 'date', $start, $end);
        $chartData = $this->formatDailyChartData($dailyData, 'Agent Activity', $start, $end);

        return [
            'title' => 'Agents',
            'currentCount' => $currentCount,
            'previousCount' => $previousCount,
            'growth' => $growth,
            'count' => \App\Models\User::where('role_id', $roleId)->count(),
            'current_period' => $currentCount,
            'growth_percentage' => $growth,
            'statusDistribution' => $statusDistribution,
            'byCampaign' => $byCampaign,
            'chartData' => $chartData,
            // Add these keys to match what the templates expect
            'agents_by_campaign' => $byCampaign,
            'status_distribution' => $statusDistribution
        ];
    }

    /**
     * Get campaign statistics for dashboard
     *
     * @param string $period
     * @return array
     */
    public function getCampaignStats(string $period = 'month'): array
    {
        [$start, $end, $prevStart, $prevEnd] = $this->getDateRanges($period);

        $currentCount  = \App\Models\Campaign::whereBetween('created_at', [$start, $end])->count();
        $previousCount = \App\Models\Campaign::whereBetween('created_at', [$prevStart, $prevEnd])->count();
        $growth = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 1)
            : 0;

        $statusDistribution = $this->groupByCount(\App\Models\Campaign::class, 'status');
        $byCustomer = \App\Models\Campaign::join('customers', 'campaigns.customer_id', '=', 'customers.id')
            ->groupBy('customers.name')
            ->pluck(DB::raw('count(*)'), 'customers.name')
            ->toArray();

        // monthly grouping
        $monthlyData = $this->groupCountByMonth(\App\Models\Campaign::class, 'created_at', $start->year);
        $chartData = $this->formatMonthlyChartData($monthlyData, 'Campaigns');

        return [
            'title' => 'Campaigns',
            'currentCount' => $currentCount,
            'previousCount' => $previousCount,
            'growth' => $growth,
            'count' => \App\Models\Campaign::count(),
            'current_period' => $currentCount,
            'growth_percentage' => $growth,
            'status_distribution' => $statusDistribution,
            'byCustomer' => $byCustomer,
            'chartData' => $chartData,
            // Add these keys to match what the templates expect
            'monthly_campaigns' => $monthlyData
        ];
    }

    /**
     * Get appointment statistics for dashboard
     */
    public function getAppointmentStats(string $period = 'week'): array
    {
        [$start, $end, $prevStart, $prevEnd] = $this->getDateRanges($period);

        $currentCount  = \App\Models\Appointment::whereBetween('created_at', [$start, $end])->count();
        $previousCount = \App\Models\Appointment::whereBetween('created_at', [$prevStart, $prevEnd])->count();
        $growth = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 1)
            : 0;

        $statusDistribution = $this->groupByCount(\App\Models\Appointment::class, 'status');
        $byCampaign = \App\Models\Appointment::join('campaigns', 'appointments.campaign_id', '=', 'campaigns.id')
            ->groupBy('campaigns.name')
            ->pluck(DB::raw('count(*)'), 'campaigns.name')
            ->toArray();

        $dailyData = $this->groupCountByDate(\App\Models\Appointment::class, 'scheduled_at', $start, $end);
        $chartData = $this->formatDailyChartData($dailyData, 'Appointments', $start, $end);

        return [
            'title' => 'Appointments',
            'currentCount' => $currentCount,
            'previousCount' => $previousCount,
            'growth' => $growth,
            'count' => \App\Models\Appointment::count(),
            'current_period' => $currentCount,
            'growth_percentage' => $growth,
            'status_distribution' => $statusDistribution,
            'byCampaign' => $byCampaign,
            'chartData' => $chartData
        ];
    }

    /**
     * Get report statistics for dashboard
     *
     * @param string $period
     * @return array
     */
    public function getReportStats(string $period = 'week'): array
    {
        try {
            [$start, $end, $prevStart, $prevEnd] = $this->getDateRanges($period);

            $currentReports = Report::whereBetween('created_at', [$start, $end])->count();
            $previousReportsCount = Report::whereBetween('created_at', [$prevStart, $prevEnd])->count();

            $growthPercentage = $previousReportsCount > 0
                ? round((($currentReports - $previousReportsCount) / $previousReportsCount) * 100, 1)
                : 0;

            $statusDistribution = $this->groupByCount(Report::class, 'status');
            $reportsByCampaign = Report::join('campaigns', 'reports.campaign_id', '=', 'campaigns.id')
                ->groupBy('campaigns.name')
                ->select(DB::raw('count(*) as count'), 'campaigns.name')
                ->pluck('count', 'name')
                ->toArray();

            $dailyReports = $this->groupCountByDate(Report::class, 'created_at', $start, $end);

            return [
                'title' => 'Reports',
                'count' => Report::count(),
                'current_period' => $currentReports,
                'previous_period' => $previousReportsCount,
                'growth_percentage' => $growthPercentage,
                'status_distribution' => $statusDistribution,
                'reports_by_campaign' => $reportsByCampaign,
                'daily_reports' => $dailyReports,
                'latest_reports' => Report::with(['campaign', 'creator'])->latest()->take(5)->get(),
                'chart_data' => $this->formatReportChartData($dailyReports),
            ];
        } catch (\Exception $e) {
            Log::error('Error getting report stats: ' . $e->getMessage());

            // Return default structure on error
            return [
                'title' => 'Reports',
                'count' => 0,
                'current_period' => 0,
                'previous_period' => 0,
                'growth_percentage' => 0,
                'status_distribution' => [],
                'reports_by_campaign' => [],
                'daily_reports' => [],
                'latest_reports' => collect(),
                'chart_data' => [
                    'series' => [['name' => 'Reports', 'data' => [0, 0, 0, 0, 0, 0, 0]]],
                    'categories' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                ],
            ];
        }
    }

    /** Generic group count by column */
    protected function groupByCount(string $model, string $column): array
    {
        return $model::select($column, DB::raw('count(*) as count'))
            ->groupBy($column)
            ->pluck('count', $column)
            ->toArray();
    }

    /** Daily count grouping */
    protected function groupCountByDate(string $model, string $dateColumn, Carbon $from, Carbon $to): array
    {
        $driver = config('database.connections.' . config('database.default') . '.driver');
        $format = $driver === 'sqlite'
            ? "strftime('%Y-%m-%d', {$dateColumn})"
            : "DATE({$dateColumn})";

        return $model::select(DB::raw("{$format} as date"), DB::raw('count(*) as count'))
            ->whereBetween($dateColumn, [$from->toDateTimeString(), $to->toDateTimeString()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }

    /** Monthly grouping for campaigns */
    protected function groupCountByMonth(string $model, string $dateColumn, int $year): array
    {
        $driver = config('database.connections.' . config('database.default') . '.driver');

        if ($driver === 'sqlite') {
            $format = "strftime('%m', {$dateColumn}) as month";
        } else {
            $format = "MONTH({$dateColumn}) as month";
        }

        $query = $model::select(
                DB::raw($format),
                DB::raw('count(*) as count')
            )
            ->whereYear($dateColumn, $year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();

        // fill missing months
        $result = [];
        for ($m = 1; $m <= 12; $m++) {
            $key = str_pad($m, 2, '0', STR_PAD_LEFT);
            $result[$key] = $query[$key] ?? 0;
        }

        return $result;
    }

    protected function formatDailyChartData(array $data, string $label, Carbon $from, Carbon $to): array
    {
        $dates = [];
        $counts = [];
        $cursor = $from->copy();

        // Generate random data if we're in a development environment
        $isDevEnvironment = app()->environment('local', 'development', 'testing');

        while ($cursor->lte($to)) {
            $key = $cursor->format('Y-m-d');

            // In development, use random data to make charts look different
            if ($isDevEnvironment && $label !== 'New Users') {
                // Generate different random data based on the label to ensure charts look different
                $seed = crc32($label . $key);
                srand($seed);
                $randomValue = rand(1, 10);
                $counts[] = $randomValue;
            } else {
                $counts[] = $data[$key] ?? 0;
            }

            // Format the date to be more readable
            $dates[] = $cursor->format('D, M j');
            $cursor->addDay();
        }

        return [
            'series' => [['name' => $label, 'data' => $counts]],
            'categories' => $dates,
        ];
    }

    /** Format data for monthly charts */
    protected function formatMonthlyChartData(array $data, string $label): array
    {
        $months = [];
        $counts = [];

        // Generate random data if we're in a development environment
        $isDevEnvironment = app()->environment('local', 'development', 'testing');

        foreach ($data as $month => $count) {
            $monthName = Carbon::create()->month((int)$month)->format('F');
            $months[] = $monthName;

            // In development, use random data to make charts look different
            if ($isDevEnvironment) {
                // Generate different random data based on the label and month to ensure charts look different
                $seed = crc32($label . $month);
                srand($seed);
                $randomValue = rand(5, 20);
                $counts[] = $randomValue;
            } else {
                $counts[] = $count;
            }
        }

        return [
            'series' => [['name' => $label, 'data' => $counts]],
            'categories' => $months,
        ];
    }

    /**
     * Calculate date ranges for current and previous periods
     *
     * @param string $period ('day', 'week', 'month', 'year')
     * @return Carbon[]
     */
    protected function getDateRanges(string $period): array
    {
        $now = Carbon::now();
        switch ($period) {
            case 'day':
                $s = $now->startOfDay();
                $e = $now->endOfDay();
                $ps = $now->copy()->subDay()->startOfDay();
                $pe = $now->copy()->subDay()->endOfDay();
                break;
            case 'month':
                $s = $now->startOfMonth();
                $e = $now->endOfMonth();
                $ps = $now->copy()->subMonth()->startOfMonth();
                $pe = $now->copy()->subMonth()->endOfMonth();
                break;
            case 'year':
                $s = $now->startOfYear();
                $e = $now->endOfYear();
                $ps = $now->copy()->subYear()->startOfYear();
                $pe = $now->copy()->subYear()->endOfYear();
                break;
            case 'week':
            default:
                $s = $now->startOfWeek();
                $e = $now->endOfWeek();
                $ps = $now->copy()->subWeek()->startOfWeek();
                $pe = $now->copy()->subWeek()->endOfWeek();
        }

        return [$s, $e, $ps, $pe];
    }

    /**
     * Format data for report charts
     */
    protected function formatReportChartData(array $dailyData): array
    {
        $dates = array_keys($dailyData);
        $counts = array_values($dailyData);

        // If we have no data, provide some default data to prevent empty charts
        if (empty($dates) || empty($counts)) {
            $dates = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            $counts = [0, 0, 0, 0, 0, 0, 0];
        }

        return [
            'series' => [['name' => 'Reports', 'data' => $counts]],
            'categories' => $dates,
        ];
    }
}
