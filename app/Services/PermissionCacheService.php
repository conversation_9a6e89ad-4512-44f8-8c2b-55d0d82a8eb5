<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Cache;

class PermissionCacheService
{
    /**
     * Get all permissions for a user.
     *
     * @param User $user
     * @return array
     */
    public static function getUserPermissions(User $user): array
    {
        return Cache::remember("user.{$user->id}.permissions", 60 * 24, function () use ($user) {
            // Get role permissions using proper relationship
            $rolePermissions = $user->role->permissions()->pluck('name')->toArray();
            
            // Get direct user permissions using proper relationship
            $directPermissions = $user->permissions()->pluck('name')->toArray();
            
            // Combine and return unique permissions
            return array_unique(array_merge($rolePermissions, $directPermissions));
        });
    }
    
    /**
     * Clear the permissions cache for a user.
     *
     * @param User $user
     * @return void
     */
    public static function clearUserPermissionsCache(User $user): void
    {
        Cache::forget("user.{$user->id}.permissions");
    }
    
    /**
     * Clear the permissions cache for all users.
     *
     * @return void
     */
    public static function clearAllUserPermissionsCache(): void
    {
        $users = User::all();
        foreach ($users as $user) {
            self::clearUserPermissionsCache($user);
        }
    }
}
