<?php

namespace App\Services;

use Spatie\Permission\Models\Permission;
use App\Models\Role;
use Illuminate\Support\Collection;

class PermissionService
{
    /**
     * Get all permissions grouped by category
     * @return array
     */
    public function getGroupedPermissions(): array
    {
        $permissions = Permission::orderBy('name')->get();
        $groupedPermissions = [];
        $permissionGroups = [];

        foreach ($permissions as $permission) {
            $group = $this->getPermissionGroup($permission->name);
            
            if (!isset($groupedPermissions[$group])) {
                $groupedPermissions[$group] = [];
                $permissionGroups[] = $group;
            }
            
            $groupedPermissions[$group][] = $permission;
        }

        // Sort groups alphabetically
        sort($permissionGroups);
        
        // Move "Other" to the end if it exists
        if (in_array('Other', $permissionGroups)) {
            $key = array_search('Other', $permissionGroups);
            unset($permissionGroups[$key]);
            $permissionGroups[] = 'Other';
        }

        return [
            'permissions' => $permissions,
            'groupedPermissions' => $groupedPermissions,
            'permissionGroups' => $permissionGroups
        ];
    }

    /**
     * Get permission group from permission name
     * @param string $permissionName
     * @return string
     */
    private function getPermissionGroup(string $permissionName): string
    {
        $parts = explode('_', $permissionName);
        if (count($parts) > 1) {
            $group = $parts[count($parts) - 1];
            
            // Handle special cases
            if ($group === 'stats') $group = 'statistics';
            if ($group === 'hr') $group = 'human resources';
            
            return ucfirst($group);
        }
        
        return 'Other';
    }

    /**
     * Get all permissions from the database
     * @return Collection
     */
    public function getAllPermissions()
    {
        return Permission::orderBy('name')->get();
    }

    /**
     * Get permission IDs from a collection or model
     * @param mixed $permissions
     * @return array
     */
    public function getPermissionIds($permissions): array
    {
        if ($permissions instanceof Collection) {
            return $permissions->pluck('id')->toArray();
        } elseif ($permissions instanceof \Illuminate\Database\Eloquent\Model) {
            return $permissions->permissions()->pluck('id')->toArray();
        } elseif (is_array($permissions)) {
            return $permissions;
        }
        
        return [];
    }

    /**
     * Toggle a permission in an array
     * @param array $permissions
     * @param int $permissionId
     * @return array
     */
    public function togglePermission(array $permissions, int $permissionId): array
    {
        if (in_array($permissionId, $permissions)) {
            return array_diff($permissions, [$permissionId]);
        }
        return array_merge($permissions, [$permissionId]);
    }

    /**
     * Toggle all permissions in a group
     * @param array $selectedPermissions
     * @param Collection $groupPermissions
     * @return array
     */
    public function toggleGroupPermissions(array $selectedPermissions, Collection $groupPermissions): array
    {
        $groupPermissionIds = $this->getPermissionIds($groupPermissions);
        
        // Check if all permissions in this group are already selected
        $allSelected = count(array_intersect($groupPermissionIds, $selectedPermissions)) === count($groupPermissionIds);
        
        if ($allSelected) {
            // Remove all permissions in this group
            return array_diff($selectedPermissions, $groupPermissionIds);
        }
        
        // Add all permissions in this group
        return array_unique(array_merge($selectedPermissions, $groupPermissionIds));
    }
}
