<?php

namespace App\View\Composers;

use Illuminate\View\View;
use App\Traits\HasPermissionChecks;

class PermissionComposer
{
    use HasPermissionChecks;

    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        $view->with([
            'hasAnyPermission' => fn($permissions) => $this->hasAnyPermission($permissions),
            'hasAllPermissions' => fn($permissions) => $this->hasAllPermissions($permissions),
            'canAccessModule' => fn($moduleId) => $this->canAccessModule($moduleId),
        ]);
    }
}
