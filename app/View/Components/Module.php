<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Module extends Component
{
    /**
     * The module ID to check access for
     * 
     * @var string
     */
    public $moduleId;

    /**
     * Create a new component instance.
     *
     * @param string $moduleId
     * @return void
     */
    public function __construct($moduleId)
    {
        $this->moduleId = $moduleId;
    }

    /**
     * Determine if the component should be rendered.
     *
     * @return bool
     */
    public function shouldRender()
    {
        return can_access_module($this->moduleId);
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return function (array $data) {
            return '{{ $slot }}';
        };
    }
}
