<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Can extends Component
{
    /**
     * The permission to check
     *
     * @var string|array
     */
    public $permission;

    /**
     * Whether to check for all permissions or any permission
     * 
     * @var bool
     */
    public $requireAll;

    /**
     * Create a new component instance.
     *
     * @param string|array $permission
     * @param bool $requireAll
     * @return void
     */
    public function __construct($permission, $requireAll = false)
    {
        $this->permission = $permission;
        $this->requireAll = $requireAll;
    }

    /**
     * Determine if the component should be rendered.
     *
     * @return bool
     */
    public function shouldRender()
    {
        if ($this->requireAll) {
            return has_all_permissions((array) $this->permission);
        }
        
        return has_any_permission((array) $this->permission);
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return function (array $data) {
            return '{{ $slot }}';
        };
    }
}
