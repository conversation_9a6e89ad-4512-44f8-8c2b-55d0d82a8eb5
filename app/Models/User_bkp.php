<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class User extends Authenticatable implements HasLocalePreference
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The cache key for user permissions.
     *
     * @var string
     */
    protected static $permissionCacheKey = 'user_permissions_';

    /**
     * The cache key for user roles.
     *
     * @var string
     */
    protected static $roleCacheKey = 'user_roles_';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'birth_date',
        'country',
        'city',
        'address',
        'email',
        'phone_number',
        'password',
        'password_confirmation',
        'role_id',
        'campaign_id',
        'department_id',
        'manager_id',
        'job_title',
        'employment_type',
        'hierarchy_level',
        'registration_number',
        'hire_date',
        'status',
        'email_notifications',
        'push_notifications',
        'sms_notifications',
        'notification_preferences',
        'locale',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birth_date' => 'date',
            'hire_date' => 'date',
            'email_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'notification_preferences' => 'json',
            'hierarchy_level' => 'integer',
        ];
    }

    /**
     * Get the user's preferred locale.
     */
    public function preferredLocale(): string
    {
        return $this->locale ?? config('app.locale');
    }

    /**
     * Route notifications for the mail channel.
     *
     * @return string
     */
    public function routeNotificationForMail(): string
    {
        return $this->email;
    }

    /**
     * Determine if the user should receive notifications.
     *
     * @param string $channel The notification channel (email, sms, push)
     * @return bool
     */
    public function shouldReceiveNotification(string $channel): bool
    {
        $channelMap = [
            'mail' => 'email_notifications',
            'email' => 'email_notifications',
            'sms' => 'sms_notifications',
            'push' => 'push_notifications',
            'database' => true, // Always receive database notifications
            'broadcast' => 'push_notifications',
        ];

        // If channel is not in the map, default to true
        if (!isset($channelMap[$channel])) {
            return true;
        }

        // If the value is boolean, return it directly
        if (is_bool($channelMap[$channel])) {
            return $channelMap[$channel];
        }

        // Otherwise, get the value from the user model
        return (bool) $this->{$channelMap[$channel]};
    }

    /**
     * Check if a specific notification type is enabled for the user.
     *
     * @param string $type The notification type (system_updates, security_alerts, etc.)
     * @return bool
     */
    public function isNotificationTypeEnabled(string $type): bool
    {
        $preferences = $this->notification_preferences ?? [];

        // If preferences is a JSON string, decode it
        if (is_string($preferences)) {
            $preferences = json_decode($preferences, true) ?? [];
        }

        // Return the preference value, defaulting to true if not set
        return $preferences[$type] ?? true;
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'users.'.$this->id;
    }

    /**
     * Check if the user has a specific role.
     *
     * @param string|int $role Role name or ID
     * @return bool
     */
    public function hasRole($role): bool
    {
        if (is_numeric($role)) {
            return $this->role_id === (int) $role;
        }

        return $this->role && $this->role->name === $role;
    }

    /**
     * Check if the user has specific permissions.
     *
     * @param string|array $permissions Permission name(s) to check
     * @param bool $requireAll Whether all permissions are required
     * @return bool
     */
    public function hasPermission($permissions, bool $requireAll = false): bool
    {
        if (is_string($permissions)) {
            $permissions = [$permissions];
        }

        // Admin has all permissions
        if ($this->role_id === 1) { // 1 is the admin role ID
            return true;
        }

        // Get cached permissions
        $userPermissions = \App\Services\PermissionCacheService::getUserPermissions($this);

        // Check if the user has the required permissions
        $hasPermissions = 0;
        foreach ($permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                $hasPermissions++;
            }
        }

        // Return based on whether we need all or any permission
        return $requireAll ? $hasPermissions === count($permissions) : $hasPermissions > 0;
    }

    /**
     * Check if the user can access a specific module.
     *
     * @param string $moduleId The module ID to check
     * @return bool
     */
    public function canAccessModule(string $moduleId): bool
    {
        $modules = config('modules');

        // Find the module by ID
        $module = collect($modules)->firstWhere('id', $moduleId);

        if (!$module) {
            return false;
        }

        // Admin can access all modules
        if ($this->role_id === 1) { // 1 is the admin role ID
            return true;
        }

        // Check if user's role is in the authorized roles
        return in_array($this->role_id, $module['authorized_roles']);
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->map(fn (string $name) => Str::of($name)->substr(0, 1))
            ->implode('');
    }

    // Relations
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function reports()
    {
        return $this->hasMany(Report::class, 'created_by');
    }

    public function training()
    {
        return $this->hasOne(Training::class);
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    /**
     * Get the user's profile picture
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function profilePicture()
    {
        return $this->media()->where('category', 'profile_picture');
    }

    /**
     * Get the user's resume
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function resume()
    {
        return $this->media()->where('category', 'resume');
    }

    /**
     * Get the user's ID card
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function idCard()
    {
        return $this->media()->where('category', 'id_card');
    }

    /**
     * Get the user's certificates
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function certificates()
    {
        return $this->media()->where('category', 'certificate');
    }

    /**
     * Get the user's other documents
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function otherDocuments()
    {
        return $this->media()->where('category', 'other_document');
    }

    /**
     * Get all documents with expiration dates
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function documentsWithExpiration()
    {
        return $this->media()->whereNotNull('expiry_date');
    }

    /**
     * Get expired documents
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function expiredDocuments()
    {
        return $this->media()->whereNotNull('expiry_date')->where('expiry_date', '<', now());
    }

    /**
     * Get documents that are about to expire
     *
     * @param int $days Number of days to check for expiration
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function documentsAboutToExpire($days = 30)
    {
        return $this->media()
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '>=', now())
            ->where('expiry_date', '<=', now()->addDays($days));
    }

    /**
     * Get documents pending verification
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function pendingVerificationDocuments()
    {
        return $this->media()->where('verification_status', 'pending');
    }

    /**
     * Get the user's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the user's permission names.
     *
     * @return array
     */
    // public function getPermissionNamesAttribute()
    // {
    //     return $this->getAllPermissions()->pluck('name')->toArray();
    // }

    /**
     * Get the user's role names.
     *
     * @return array
     */
    public function getRoleNamesAttribute()
    {
        return $this->getRoleNames()->toArray();
    }



    /**
     * Check if the user has any of the given permissions.
     *
     * @param  array|string  $permissions
     * @return bool
     */
    public function hasAnyPermission($permissions)
    {
        if ($this->isSuperAdmin()) {
            return true;
        }

        $permissions = is_array($permissions) ? $permissions : func_get_args();

        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the user has all of the given permissions.
     *
     * @param  array|string  $permissions
     * @return bool
     */
    public function hasAllPermissions($permissions)
    {
        if ($this->isSuperAdmin()) {
            return true;
        }

        $permissions = is_array($permissions) ? $permissions : func_get_args();

        foreach ($permissions as $permission) {
            if (! $this->hasPermission($permission)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the user is a super admin.
     *
     * @return bool
     */
    public function isSuperAdmin()
    {
        return $this->hasRole('super_admin');
    }

    /**
     * Get all permissions for the user.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllPermissions()
    {
        Log::info('Loading permissions for user: ' . $this->name);
        
        $cacheKey = self::$permissionCacheKey . $this->id;

        return Cache::remember($cacheKey, now()->addDay(), function () {
            Log::info('Getting fresh permissions from database for user: ' . $this->name);
            
            // Load fresh permissions from database
            $rolePermissions = $this->role->permissions()->get();
            Log::info('Role permissions found: ' . json_encode($rolePermissions->pluck('name')));
            
            $directPermissions = $this->permissions()->get();
            Log::info('Direct permissions found: ' . json_encode($directPermissions->pluck('name')));
            
            $allPermissions = $rolePermissions
                ->merge($directPermissions)
                ->unique('name');
                
            Log::info('Total permissions for user: ' . $this->name . ' - ' . json_encode($allPermissions->pluck('name')));
            
            return $allPermissions;
        });
    }

    /**
     * Clear the user's permission cache.
     *
     * @return void
     */
    public function clearPermissionCache()
    {
        Cache::forget(self::$permissionCacheKey . $this->id);
        Cache::forget(self::$roleCacheKey . $this->id);
    }

    /**
     * Get the URL for the user's profile picture
     *
     * @return string
     */
    public function getProfilePictureUrl()
    {
        $profilePicture = $this->profilePicture()->first();

        if ($profilePicture) {
            // Use the documents.view route for consistency across the application
            return route('documents.view', ['id' => $profilePicture->id]);
        }

        // Fallback to default image
        return asset('images/default-profile.png');
    }

    /**
     * Get all unread notifications for the user
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function unreadNotifications()
    {
        return $this->notifications()->whereNull('read_at');
    }

    /**
     * Mark all notifications as read
     *
     * @return void
     */
    public function markAllNotificationsAsRead()
    {
        $this->unreadNotifications()->update(['read_at' => now()]);
    }

    // Site management relationships
    public function managedSites()
    {
        return $this->hasMany(Site::class, 'manager_id');
    }

    public function managedSitesAsIT()
    {
        return $this->hasMany(Site::class, 'it_manager_id');
    }

    public function managedSitesAsTrainer()
    {
        return $this->hasMany(Site::class, 'trainer_id');
    }

    public function managedSitesAsAccountant()
    {
        return $this->hasMany(Site::class, 'accountant_id');
    }

    public function managedSitesAsHR()
    {
        return $this->hasMany(Site::class, 'hr_manager_id');
    }

    // Platform management relationships
    public function managedPlatforms()
    {
        return $this->hasMany(Platform::class, 'manager_id');
    }

    public function supportedPlatforms()
    {
        return $this->hasMany(Platform::class, 'it_support_id');
    }

    // Campaign management relationships
    public function supervisedCampaigns()
    {
        return $this->hasMany(Campaign::class, 'manager_id');
    }

    /**
     * Get the campaigns this user is assigned to.
     * This is a many-to-many relationship through campaign_user pivot table.
     */
    public function campaigns()
    {
        return $this->belongsToMany(Campaign::class, 'campaign_user')
            ->withPivot('role', 'assigned_at', 'status')
            ->withTimestamps();
    }

    // Role helper methods
    public function isDirector()
    {
        return $this->role_id === 2;
    }

    public function isSiteManager()
    {
        return $this->role_id === 3;
    }

    public function isPlatformManager()
    {
        return $this->role_id === 4;
    }

    public function isSupervisor()
    {
        return $this->role_id === 5;
    }

    public function isAgent()
    {
        return $this->role_id === 6;
    }

    public function isITManager()
    {
        return $this->role_id === 8;
    }

    public function isITSupport()
    {
        return $this->role_id === 9;
    }

    public function isTrainer()
    {
        return $this->role_id === 10;
    }

    public function isAccountant()
    {
        return $this->role_id === 11;
    }

    public function isHRManager()
    {
        return $this->role_id === 12;
    }

    // Add new relationships for user roles
    public function directedCallCenter()
    {
        return $this->hasOne(CallCenter::class, 'director_id');
    }

    // Helper method for campaign supervisors
    public function isCampaignSupervisor()
    {
        return $this->role_id === 5; // Campaign supervisor role ID
    }

    /**
     * Get the department this user belongs to.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the manager of this user.
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the direct reports (subordinates) of this user.
     */
    public function directReports()
    {
        return $this->hasMany(User::class, 'manager_id');
    }

    /**
     * Get all teams this user is a member of.
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_user')
            ->withPivot('role', 'joined_at', 'left_at')
            ->withTimestamps();
    }

    /**
     * Get all active teams this user is a member of.
     */
    public function activeTeams()
    {
        return $this->teams()
            ->wherePivotNull('left_at')
            ->where('status', 'active');
    }

    /**
     * Get all teams this user leads.
     */
    public function ledTeams()
    {
        return $this->hasMany(Team::class, 'leader_id');
    }

    /**
     * Get the managed department.
     */
    public function managedDepartment()
    {
        return $this->hasOne(Department::class, 'manager_id');
    }

    /**
     * Get all subordinates (direct and indirect reports).
     */
    public function allSubordinates()
    {
        $directReports = $this->directReports;
        $allSubordinates = $directReports->toArray();

        foreach ($directReports as $report) {
            $allSubordinates = array_merge($allSubordinates, $report->allSubordinates()->toArray());
        }

        return collect($allSubordinates);
    }

    /**
     * Check if this user is a manager of another user.
     */
    public function isManagerOf(User $user): bool
    {
        return $user->manager_id === $this->id ||
               $user->manager && $this->isManagerOf($user->manager);
    }

    /**
     * Get the user's organizational path (from top of hierarchy to this user).
     */
    public function getOrganizationalPath()
    {
        $path = collect([$this]);
        $currentUser = $this;

        while ($currentUser->manager) {
            $path->prepend($currentUser->manager);
            $currentUser = $currentUser->manager;
        }

        return $path;
    }

    // Site personnel relationship
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id')
            ->withTimestamps()
            ->whereNull('deleted_at');
    }



    public function sites()
    {
        return $this->belongsToMany(Site::class, 'site_user', 'user_id', 'site_id')
            ->withPivot('role', 'assigned_at')
            ->withTimestamps();
    }

    /**
     * Get the skills that the user has.
     */
    public function skills()
    {
        return $this->belongsToMany(Skill::class, 'user_skills')
            ->withPivot('proficiency_level', 'notes', 'acquired_at', 'last_verified_at', 'verified_by')
            ->withTimestamps();
    }

    /**
     * Get the certifications that the user has.
     */
    public function certifications()
    {
        return $this->belongsToMany(Certification::class, 'user_certifications')
            ->withPivot('certificate_number', 'issued_at', 'expires_at', 'status', 'notes', 'document_path', 'verified_by')
            ->withTimestamps();
    }

    /**
     * Get the user's active certifications.
     */
    public function activeCertifications()
    {
        return $this->certifications()
            ->wherePivot('status', 'active')
            ->wherePivot('expires_at', '>', now());
    }

    /**
     * Get the user's expired certifications.
     */
    public function expiredCertifications()
    {
        return $this->certifications()
            ->where(function($query) {
                $query->wherePivot('status', 'expired')
                    ->orWhere(function($q) {
                        $q->wherePivot('status', 'active')
                          ->wherePivot('expires_at', '<', now());
                    });
            });
    }

    /**
     * Check if the user has a specific skill with the required proficiency level.
     *
     * @param int $skillId
     * @param int $requiredLevel
     * @return bool
     */
    public function hasSkill($skillId, $requiredLevel = 1)
    {
        $skill = $this->skills()->where('skill_id', $skillId)->first();

        if (!$skill) {
            return false;
        }

        return $skill->pivot->proficiency_level >= $requiredLevel;
    }

    /**
     * Check if the user has a specific active certification.
     *
     * @param int $certificationId
     * @return bool
     */
    public function hasCertification($certificationId)
    {
        return $this->activeCertifications()
            ->where('certification_id', $certificationId)
            ->exists();
    }

    /**
     * Check if the user meets all skill requirements for a campaign.
     *
     * @param Campaign $campaign
     * @return bool
     */
    public function meetsSkillRequirementsFor(Campaign $campaign)
    {
        $requiredSkills = $campaign->requiredSkills;

        foreach ($requiredSkills as $requiredSkill) {
            if ($requiredSkill->pivot->is_mandatory &&
                !$this->hasSkill($requiredSkill->id, $requiredSkill->pivot->minimum_proficiency_level)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the user meets all certification requirements for a campaign.
     *
     * @param Campaign $campaign
     * @return bool
     */
    public function meetsCertificationRequirementsFor(Campaign $campaign)
    {
        $requiredCertifications = $campaign->requiredCertifications;

        foreach ($requiredCertifications as $requiredCertification) {
            if ($requiredCertification->pivot->is_mandatory &&
                !$this->hasCertification($requiredCertification->id)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the user is eligible for a campaign based on skills and certifications.
     *
     * @param Campaign $campaign
     * @return bool
     */
    public function isEligibleFor(Campaign $campaign)
    {
        return $this->meetsSkillRequirementsFor($campaign) &&
               $this->meetsCertificationRequirementsFor($campaign);
    }

    /**
     * Get the calls handled by this agent.
     */
    public function calls()
    {
        return $this->hasMany(Call::class);
    }

    /**
     * Get the call evaluations for this agent.
     */
    public function callEvaluations()
    {
        return $this->hasManyThrough(CallEvaluation::class, Call::class);
    }

    /**
     * Get the performance metrics for this agent.
     */
    public function performanceMetrics()
    {
        return $this->hasMany(PerformanceMetric::class);
    }

    /**
     * Get the KPI targets for this agent.
     */
    public function kpiTargets()
    {
        return $this->hasMany(KpiTarget::class);
    }

    /**
     * Get the evaluations performed by this user (as an evaluator).
     */
    public function evaluationsPerformed()
    {
        return $this->hasMany(CallEvaluation::class, 'evaluator_id');
    }

    /**
     * Get the performance reviews for this user.
     */
    public function performanceReviews()
    {
        return $this->hasMany(PerformanceReview::class);
    }

    /**
     * Get the performance reviews conducted by this user (as a reviewer).
     */
    public function reviewsPerformed()
    {
        return $this->hasMany(PerformanceReview::class, 'reviewer_id');
    }

    /**
     * Get the onboarding processes for this user.
     */
    public function onboardings()
    {
        return $this->hasMany(EmployeeOnboarding::class);
    }

    /**
     * Get the current active onboarding process for this user.
     */
    public function activeOnboarding()
    {
        return $this->onboardings()
            ->whereIn('status', ['pending', 'in_progress'])
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Get the onboarding processes assigned to this user (as HR or manager).
     */
    public function assignedOnboardings()
    {
        return $this->hasMany(EmployeeOnboarding::class, 'assigned_to');
    }

    /**
     * Get the observations for this agent.
     */
    public function observations()
    {
        return $this->hasMany(Observation::class, 'agent_id');
    }

    /**
     * Get the training sessions this user is enrolled in.
     */
    public function trainingSessions()
    {
        return $this->belongsToMany(TrainingSession::class, 'training_session_user')
            ->withPivot('status', 'enrollment_date', 'completion_date', 'grade')
            ->withTimestamps();
    }

    /**
     * Get the completed training modules for this user.
     */
    public function completedModules()
    {
        return $this->belongsToMany(TrainingModule::class, 'agent_module_completion')
            ->withPivot('completion_date', 'grade', 'notes')
            ->withTimestamps();
    }
}
