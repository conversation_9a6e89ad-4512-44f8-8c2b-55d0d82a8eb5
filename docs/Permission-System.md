# Permission System Documentation

## Overview

This document outlines the permission system implemented in the application. The system is built on top of Laravel's built-in authorization features and the `spatie/laravel-permission` package, with additional custom functionality for module-based access control.

## Core Concepts

### Permissions
- Granular access control for specific actions (e.g., `create_users`, `edit_users`)
- Can be assigned directly to users or through roles
- Checked using `@permission` directive or `has_any_permission()`/`has_all_permissions()` helpers

### Roles
- Collections of permissions
- Users can have multiple roles
- Common roles: `admin`, `manager`, `agent`
- Checked using `@role` directive

### Modules
- Logical groupings of related functionality
- Each module has a unique ID (e.g., `users`, `reports`)
- Can be enabled/disabled per user/role
- Checked using `@module` directive or `can_access_module()` helper

## Usage

### Blade Directives

```blade
@permission('edit_users')
    {{-- Content for users with 'edit_users' permission --}}
@endpermission

@role('admin')
    {{-- Content for admin users --}}
@endrole

@module('reports')
    {{-- Content for users with access to reports module --}}
@endmodule
```

### Blade Components

```blade
<x-can permission="edit_users">
    {{-- Content for users who can edit users --}}
    
    <x-can :permission="['delete_users', 'manage_users']" :require-all="true">
        {{-- Requires ALL specified permissions --}}
    </x-can>
</x-can>

<x-module module="reports">
    {{-- Content for users with access to reports module --}}
</x-module>
```

### Helper Functions

```php
// Check if user has any of the given permissions
if (has_any_permission(['edit_users', 'manage_users'])) {
    // User can edit or manage users
}

// Check if user has all of the given permissions
if (has_all_permissions(['create_users', 'edit_users'])) {
    // User can both create and edit users
}

// Check module access
if (can_access_module('reports')) {
    // User has access to reports module
}

// Generate route only if user has permission
$userProfileRoute = module_route('users.profile', ['id' => 1]);

// Generate link only if user has permission
{!! module_link('users.index', 'View All Users', [], ['class' => 'btn btn-info']) !!}
```

### Middleware

```php
// Protect routes with specific permissions
Route::get('/admin/users', 'UserController@index')->middleware('permission:view_users');

// Multiple permissions (user needs any of them)
Route::get('/admin/users', 'UserController@index')
    ->middleware('permission:view_users,edit_users');

// Require all specified permissions
Route::get('/admin/users', 'UserController@index')
    ->middleware('permission:view_users|edit_users|require_all');
```

## Best Practices

1. **Be Specific**: Use the most specific permission that fits the action
2. **Use Module Access**: Group related permissions under modules for better organization
3. **Check Early**: Perform permission checks as early as possible in the request lifecycle
4. **UI Feedback**: Provide clear feedback when users don't have access to certain features
5. **Test Thoroughly**: Always test permission checks with different user roles

## Example Implementation

### Controller

```php
public function edit(User $user)
{
    $this->authorize('edit_users');
    
    // Your edit logic here
}
```

### View

```blade
@can('edit_users')
    <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">
        Edit User
    </a>
@endcan
```

## Troubleshooting

- **Permission not working?**
  - Check if the permission is assigned to the user or their role
  - Verify the permission name matches exactly
  - Clear the permission cache: `php artisan permission:cache-reset`

- **Module access issues?**
  - Ensure the module is enabled in `config/modules.php`
  - Check if the user has the required permissions for the module

## Development

To test the permission system locally, visit `/permission-examples` when running in the local environment.
